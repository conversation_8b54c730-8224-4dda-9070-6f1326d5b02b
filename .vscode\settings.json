{"files.associations": {"functional": "cpp", "array": "cpp", "*.tcc": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "memory": "cpp", "new": "cpp", "ostream": "cpp", "numeric": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "cinttypes": "cpp", "utility": "cpp", "typeinfo": "cpp", "string": "cpp", "random": "cpp", "regex": "cpp"}, "cmake.sourceDirectory": "/Users/<USER>/Desktop/esp32/ESP32-MLTECH/.pio/libdeps/esp32doit-devkit-v1/AsyncTCP", "cmake.configureOnOpen": false}