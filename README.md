# ESP32 FIRMWARE FOR MLTECH's PRODUCTS
[![MLTECH](https://mltech.com.vn/wp-content/uploads/2022/09/cropped-logo-mltech-350x100-02.png)](https://mltech.com.vn/)

## _1. Link cửa hàng_
## _2. Hướng dẫn sử dụng source code_
Thay đổi đường dẫn trong file [platformio.init](./platformio.ini) 
```
src_dir=example/Thingsboard
```

## _3. Cấu trúc file code_
```
ESP32 FIRM/
    |-- application                                
    |   |-- docs
    |   |-- name_application
    |   |   |-- src.h
    |   |   |-- scr.cpp
    |-- examples                                   
    |   |-- name_example
    |   |   |-- scr.h
    |   |   |-- scr.cpp
    |-- data                                      
    |   |-- device_infor.json
    |   |-- firmware_infor.json   
    |   |-- network_service.json
    |   |-- schedule.json
    |   |-- thingsboard_credential.json
    |-- lib                                                   
    |   |-- bar
    |   |   |--docs
    |   |   |--examples
    |   |   |--src
    |   |   |   |--bar.c
    |   |   |   |--bar.h
    |   |   |--test
    |   |   |   |--test.c
    |   |   |   |--test.cpp
    |   |   |--README.md
    |   |   |-- board_lib
    |   |   |   |--board_name
    |   |   |   |--docs
    |   |   |   |   |--board_name.h
    |   |   |   |   |--board_name.cpp
    |-- scr
    |   |-- main.cpp
    |--platformio.ini

   

```
## _3. Example_
Xem code mẫu [tại đây](./examples)
```
Examples/
    |-- Thingsboard
    |   |-- ThingsboardOTA              #Example Thingsboard OTA
    |   |-- ThingsboardTemplate
    |   |-- ThingsboardSchedule
    |--Modbus
```
## _4. Application code_
Xem code sản phẩm [tại đây](./application)