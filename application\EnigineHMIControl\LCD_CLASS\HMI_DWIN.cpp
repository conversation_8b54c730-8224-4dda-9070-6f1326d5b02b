#include "HMI_DWIN.h"

HMI_DWIN::HMI_DWIN()
{
    
}

HMI_DWIN::HMI_DWIN(Stream *port)
{
    dwin=new MLDwin(port);
}
void HMI_DWIN::setPage(int page)
{
    dwin->setBuzzer(10);
    dwin->dwinShowPage(page);
}

void HMI_DWIN::setParameterValue(parameter_value_struct_t parameter)
{
    parameter_value = parameter;
}

void HMI_DWIN::loop()
{
    if(millis()-preTimeUpdate>1000){
        dwin->writeVP(COOLANT_TEMP_ADDR,parameter_value.coolant_temp);
        dwin->writeVP(OILD_PRESS_ADDR,parameter_value.oil_press);
        dwin->writeVP(XMSN_TEMP_ADDR,parameter_value.xmsn_temp);
        dwin->writeVP(XMSN_PRES_ADDR,parameter_value.xmsn_pres);
        dwin->writeVP(BOOST_PRES_ADDR,parameter_value.boost_pres);
        dwin->writeVP(ENGINE_LOAD_ADDR,parameter_value.engine_load);
        dwin->writeVP(FUEL_RATE_ADDR,parameter_value.fuel_rate);
        dwin->writeVP(SPEED_ADDR,parameter_value.speed);
        dwin->writeVP(BATTERY_ADDR,parameter_value.battery);
        dwin->writeVP(FUEL_DIFF_PRES_ADDR,parameter_value.fuel_diff_pres);
        dwin->writeVP(FUEL_PRES_ADDR,parameter_value.fuel_pres);
        dwin->writeVP(MAINIFORD_TEMP_ADDR,parameter_value.mainiford_temp);
        dwin->writeVP(FUEL_TEMP_ADDR,parameter_value.fuel_temp);
        dwin->writeVP(ENGINE_HOURS_ADDR,parameter_value.engine_hours);

        dwin->writeVP(TRIP_ENGINE_HOURS_ADDR,parameter_value.trip_engine_hours);
        dwin->writeVP(TRIP_IDLE_HOURS_ADDR,parameter_value.trip_idle_hours);
        dwin->writeVP(TRIP_FUEL_ADDR,parameter_value.trip_fuel);
        dwin->writeVP(TRIP_IDLE_FUEL_ADDR,parameter_value.trip_idle_fuel);
        dwin->writeVP(AVG_FUEL_CONSUMTION_ADDR,parameter_value.avg_fuel_consumption);

        dwin->writeVP(LIFETIME_ENGINE_HOURS,parameter_value.lifetime_engine_hours);
        dwin->writeVP(LIFETIME_IDLE_HOURS_ADDR,parameter_value.lifetime_idle_hours);
        dwin->writeVP(LIFETIME_FUEL_ADDR,parameter_value.lifetime_fuel);
        dwin->writeVP(LIFETIME_IDLE_FUEL_ADDR,parameter_value.lifetime_idle_fuel);
        dwin->writeVP(LIFETIME_AVG_FUEL_CONSUMTION_ADDR,parameter_value.lifrtime_avg_fuel_consumpiton);

        preTimeUpdate=millis();
    }
}

HMI_DWIN::~HMI_DWIN()
{
}
