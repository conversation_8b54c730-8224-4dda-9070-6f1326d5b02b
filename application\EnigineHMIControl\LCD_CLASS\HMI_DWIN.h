#ifndef _HMI_DWIN_H_
#define _HMI_DWIN_H_

#include "Arduino.h"
#include "MLDwin.h"
#include "../common.h"
#include "MLDwin.h"

class HMI_DWIN
{
private:
    MLDwin *dwin;
    parameter_value_struct_t parameter_value;
    unsigned long preTimeUpdate;

public:
    HMI_DWIN(/* args */);
    HMI_DWIN(Stream *port);
    ~HMI_DWIN();
        void setPage(int page);
        void setParameterValue(parameter_value_struct_t parameter);
        void loop();

};

#endif
