#include <SPI.h>

#include "SAE1939.h"

CAN_device_t CAN_cfg;
// write constructor
// write detail function of class SAE1939
SAE1939::SAE1939()
{
  // CAN_cfg = new CAN_device_t;
}

SAE1939::~SAE1939()
{
  // delete CAN_cfg;
}

void SAE1939::Init()
{
  /* set CAN pins and baudrate */
  CAN_cfg.speed = CAN_SPEED_250KBPS;
  CAN_cfg.tx_pin_id = GPIO_NUM_5;
  CAN_cfg.rx_pin_id = GPIO_NUM_4;
  /* create a queue for CAN receiving */
  CAN_cfg.rx_queue = xQueueCreate(10, sizeof(CAN_frame_t));
  // initialize CAN Module
  ESP32Can.CANInit();
}

void SAE1939::receivedData()
{
  static unsigned long pngID = 0;
  if (xQueueReceive(CAN_cfg.rx_queue, &rx_frame, 3 * portTICK_PERIOD_MS) == pdTRUE)
  {
    if (rx_frame.FIR.B.RTR == CAN_RTR)
      printf(" RTR from 0x%08x, DLC %d\r\n", rx_frame.MsgID, rx_frame.FIR.B.DLC);
    else
    {
      printf(" from 0x%08x, DLC %d\r\n", rx_frame.MsgID, rx_frame.FIR.B.DLC);
      pngID = rx_frame.MsgID;
      pngID = pngID << 8;
      pngID = pngID >> 16;
      pgnID = pngID;
      Serial.print("PGN = ");
      Serial.println(pngID, HEX);

      for (int i = 0; i < 8; i++)
      {
        //   printf("%c\t", (char)rx_frame.data.u8[i]);
        data[i] = rx_frame.data.u8[i];
        printf("%d\t", rx_frame.data.u8[i]);
      }
      printf("\n");
    }
  }
}

void SAE1939::dataProcessing()
{
  switch (pgnID)
  {
  case TEMP_SENSOR_CODE:
  {
    int coolt = data[0];
    parameter_value.coolant_temp = ((-40) + coolt);
    // if(parameter_value.coolant_temp > 100) parameter_value.coolant_temp = 0;
    Serial.println("TEMP_SENSOR_CODE");
    break;
  }
  case ENGINE_SPEED_CODE:
  {
    unsigned int r1 = data[3];
    unsigned int r2 = data[4];
    parameter_value.speed = ((r1) + (r2 * 256)) * 0.125;
    if (parameter_value.speed > 10000)
      parameter_value.speed = 0;
    if (parameter_value.speed > 6000)
      parameter_value.speed = 0;
    // float rpm = ((r1) + (r2 * 256)) * 0.125;
    Serial.println("ENGINE_SPEED_CODE");
    break;
  }

  case FUEL_PRESSURE_CODE:
  {
    unsigned int foilp = data[0];
    unsigned int loilp = data[3];
    parameter_value.oil_press = (foilp * 4);
    parameter_value.fuel_pres = (loilp * 4);
    // float loilpressure = (loilp * 4);
    // float foilpressure = (foilp * 4);
    Serial.println("FUEL_PRESSURE_CODE");
    break;
  }
  case BOOST_PRESSURE_CODE:
  {
    unsigned int bstp = data[1];
    parameter_value.boost_pres = (bstp * 2);
    // float boostpressure = (bstp * 2);
    Serial.println("BOOST_PRESSURE_CODE");
    break;
  }
  case ENGINE_POWER_CODE: // hien tai dang sai
  {
    unsigned int p1 = data[2];
    // float rpower = ((p1 * 0.01)*1125);
    parameter_value.engine_hours = ((p1 * 0.01) * 1125);
    Serial.println("ENGINE_POWER_CODE");
    break;
  }

  case BATTERY_CODE:
  {
    unsigned int b1 = data[4];
    unsigned int b2 = data[5];
    float battery = ((b1) + (b2 * 256)) * 0.05;
    parameter_value.battery = battery;
    Serial.println("BATTERY_CODE");
    break;
  }
    
  case ENGINE_HOURS_CODE:
  { 
    unsigned int h1 = data[0];
    unsigned int h2 = data[1];
    unsigned int h3 = data[2];
    unsigned int h4 = data[3];
    parameter_value.engine_hours = (((h1) + (h2 * 256) + (h3) + (h4))) * 0.05;
    // float hours = (((h1) + (h2 * 256) + (h3) + (h4))) * 0.05;
    Serial.println("ENGINE_HOURS_CODE");
    break;
  }
    
  case FUEL_RATE_CODE:
  {
    unsigned int f1 = data[0];
    unsigned int f2 = data[1];
    // float flow = ((f1) + (f2 * 256)) * 0.05;
    parameter_value.fuel_rate = ((f1) + (f2 * 256)) * 0.05;
    Serial.println("FUEL_RATE_CODE");
    break;
  }
    
  }
  // switch (pgnID)
  // {
  // case TEMP_SENSOR_CODE:
  //   int coolt = data[0];
  //   parameter_value.coolant_temp = ((-40) + coolt);
  //   // if(parameter_value.coolant_temp > 100) parameter_value.coolant_temp = 0;
  //   Serial.println("TEMP_SENSOR_CODE");
  //   break;
  // case ENGINE_SPEED_CODE:
  //   unsigned int r1 = data[3];
  //   unsigned int r2 = data[4];
  //   parameter_value.speed = ((r1) + (r2 * 256)) * 0.125;
  //    if (parameter_value.speed > 10000) parameter_value.speed = 0;
  //   if (parameter_value.speed > 6000) parameter_value.speed = 0;
  //   // float rpm = ((r1) + (r2 * 256)) * 0.125;
  //   Serial.println("ENGINE_SPEED_CODE");
  //   break;
  // case FUEL_PRESSURE_CODE:
  //    unsigned int foilp = data[0];
  //   unsigned int loilp = data[3];
  //   parameter_value.oil_press = (foilp * 4);
  //   parameter_value.fuel_pres = (loilp * 4);
  //   // float loilpressure = (loilp * 4);
  //   // float foilpressure = (foilp * 4);

  //   Serial.println("FUEL_PRESSURE_CODE");
  //   break;
  // case BOOST_PRESSURE_CODE:
  //   unsigned int bstp = data[1];
  //   parameter_value.boost_pres = (bstp * 2);
  //   // float boostpressure = (bstp * 2);
  //   Serial.println("BOOST_PRESSURE_CODE");
  //   break;
  // case ENGINE_POWER_CODE:   // hien tai dang sai
  //   unsigned int p1 = data[2];
  //   // float rpower = ((p1 * 0.01)*1125);
  //   parameter_value.engine_hours = ((p1 * 0.01)*1125);
  //   Serial.println("ENGINE_POWER_CODE");
  //   break;
  // case BATTERY_CODE:
  //   unsigned int b1 = data[4];
  //   unsigned int b2 = data[5];
  //   float battery = ((b1) + (b2 * 256)) * 0.05;
  //   parameter_value.battery = battery;
  //   Serial.println("BATTERY_CODE");
  //   break;
  // case ENGINE_HOURS_CODE:
  //   unsigned int h1 = data[0];
  //   unsigned int h2 = data[1];
  //   unsigned int h3 = data[2];
  //   unsigned int h4 = data[3];
  //   parameter_value.engine_hours = (((h1) + (h2 * 256) + (h3) + (h4)))*0.05;
  //   // float hours = (((h1) + (h2 * 256) + (h3) + (h4))) * 0.05;
  //   Serial.println("ENGINE_HOURS_CODE");
  //   break;
  // case FUEL_RATE_CODE:
  //   unsigned int f1 = data[0];
  //   unsigned int f2 = data[1];
  //   // float flow = ((f1) + (f2 * 256)) * 0.05;
  //   parameter_value.fuel_rate = ((f1) + (f2 * 256)) * 0.05;
  //   Serial.println("FUEL_RATE_CODE");
  //   break;
  // }
}

void SAE1939::transmitData()
{

  tx_frame.FIR.B.FF = CAN_frame_ext;
  tx_frame.MsgID = 0x0CF00300;
  tx_frame.FIR.B.DLC = 8;
  tx_frame.data.u8[0] = 0x01;
  tx_frame.data.u8[1] = 0x02;
  tx_frame.data.u8[2] = 0x03;
  tx_frame.data.u8[3] = 0x60;
  tx_frame.data.u8[4] = 0x6C;
  tx_frame.data.u8[5] = 0x02;
  tx_frame.data.u8[6] = 0x02;
  tx_frame.data.u8[7] = 0x00;

  if (millis() - preTime >= 1000)
  {
    preTime = millis();
    ESP32Can.CANWriteFrame(&tx_frame);
    Serial.println("transmit data");
  }
}
