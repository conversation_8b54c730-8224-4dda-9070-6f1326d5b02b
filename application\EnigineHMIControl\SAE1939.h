#ifndef SAE1939_H
#define SAE1939_H

#include <Arduino.h>
#include <ESP32CAN.h>
#include <CAN_config.h>
#include "common.h"

// Compiler Settings
#define OK 0
#define ERR 1

// Debugger Settings
#define DEBUG 0


// #define COOLANT_TEMP_ADDR                       0x1000
// #define OILD_PRESS_ADDR                         0x1002
// #define XMSN_TEMP_ADDR                          0x1004
// #define XMSN_PRES_ADDR                          0x1006
// #define SPEED_ADDR                              0x1008
// #define BATTERY_ADDR                            0x1010
// #define BOOST_PRES_ADDR                         0x1012
// #define ENGINE_LOAD_ADDR                        0x1014
// #define FUEL_RATE_ADDR                          0x1016
// #define FUEL_DIFF_PRES_ADDR                     0x1018
// #define MAINIFORD_TEMP_ADDR                     0x1020
// #define ENGINE_HOURS_ADDR                       0x1022 
// #define TRIP_ENGINE_HOURS_ADDR                  0x1024
// #define TRIP_IDLE_HOURS_ADDR                    0x1026
// #define TRIP_FUEL_ADDR                          0x1028
// #define TRIP_IDLE_FUEL_ADDR                     0x1030
// #define AVG_FUEL_CONSUMTION_ADDR                0x1032
// #define LIFETIME_ENGINE_HOURS                   0x1034
// #define LIFETIME_IDLE_HOURS_ADDR                0x1036
// #define LIFETIME_FUEL_ADDR                      0x1038
// #define LIFETIME_IDLE_FUEL_ADDR                 0x1040
// #define LIFETIME_AVG_FUEL_CONSUMTION_ADDR       0x1042
// #define FUEL_PRES_ADDR                          0x1046
// #define FUEL_TEMP_ADDR                          0x1048

#define TEMP_SENSOR_CODE    0xfeee //Temp Sensor Code/
#define ENGINE_SPEED_CODE     0xf004 //Engine Speed Code/
#define FUEL_PRESSURE_CODE     0xfeef //Fuel, Oil Pressure Code/
#define BOOST_PRESSURE_CODE     0xfef6 //Boost Pressure Code/
#define ENGINE_POWER_CODE     0xf003 //Engine Power Code/
#define BATTERY_CODE     0xfef7 //Battery Code/
#define ENGINE_HOURS_CODE     0xfee5 //Engine Hours Code/
#define FUEL_RATE_CODE     0xfef2 //Fuel Rate Code/

// write class SAE1939
class SAE1939
{
private:
    // CAN_device_t CAN_cfg
    uint32_t pgnID = 0;
    CAN_frame_t tx_frame;
    CAN_frame_t rx_frame;
       unsigned long preTime ;

public:
    int data[8];
    parameter_value_struct_t parameter_value;
    SAE1939();
    ~SAE1939();
    void Init();
    void receivedData();
    void dataProcessing();
    void transmitData();
};

#endif