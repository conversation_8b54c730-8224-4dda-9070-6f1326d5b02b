#ifndef _COMMON_H_
#define _COMMON_H_


#define TX_HMI 21
#define RX_HMI 22

#define TX_CAN
#define RX_CAN

#define NEXT_BUTTON 13
#define PRE_BUTTON  14
#define ALARM_BUTTON    27

#define MAX_PAGE 6

#define COOLANT_TEMP_ADDR                       0x1000
#define OILD_PRESS_ADDR                         0x1002
#define XMSN_TEMP_ADDR                          0x1004
#define XMSN_PRES_ADDR                          0x1006
#define SPEED_ADDR                              0x1008
#define BATTERY_ADDR                            0x1010
#define BOOST_PRES_ADDR                         0x1012
#define ENGINE_LOAD_ADDR                        0x1014
#define FUEL_RATE_ADDR                          0x1016
#define FUEL_DIFF_PRES_ADDR                     0x1018
#define MAINIFORD_TEMP_ADDR                     0x1020
#define ENGINE_HOURS_ADDR                       0x1022 
#define TRIP_ENGINE_HOURS_ADDR                  0x1024
#define TRIP_IDLE_HOURS_ADDR                    0x1026
#define TRIP_FUEL_ADDR                          0x1028
#define TRIP_IDLE_FUEL_ADDR                     0x1030
#define AVG_FUEL_CONSUMTION_ADDR                0x1032
#define LIFETIME_ENGINE_HOURS                   0x1034
#define LIFETIME_IDLE_HOURS_ADDR                0x1036
#define LIFETIME_FUEL_ADDR                      0x1038
#define LIFETIME_IDLE_FUEL_ADDR                 0x1040
#define LIFETIME_AVG_FUEL_CONSUMTION_ADDR       0x1042
#define FUEL_PRES_ADDR                          0x1046
#define FUEL_TEMP_ADDR                          0x1048


typedef struct{
    int coolant_temp;
    int oil_press;
    int xmsn_temp;
    int xmsn_pres;
    int speed;
    int battery;
    int boost_pres;
    int engine_load;
    int fuel_rate;
    int fuel_diff_pres;
    int mainiford_temp;

    float engine_hours;
    float trip_engine_hours;
    float trip_idle_hours;
    float trip_fuel;
    float avg_fuel_consumption;
    float lifetime_engine_hours;
    float lifetime_idle_hours;
    float lifetime_fuel;
    float lifetime_idle_fuel;
    float lifrtime_avg_fuel_consumpiton;
    float fuel_pres;
    float fuel_temp;
    float trip_idle_fuel;

}parameter_value_struct_t;


#endif