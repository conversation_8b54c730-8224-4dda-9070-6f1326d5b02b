#include "main.h"
#include "Adafruit_I2CDevice.h"
void setup()
{
    Serial.begin(115200);
    Serial.println("CaterPillar CANBUS Application Started");
    j1939.Init();
    // uart_dwin.begin(115200, SERIAL_8N1, RX_HMI, TX_HMI);
    pinMode(NEXT_BUTTON, INPUT);
    pinMode(PRE_BUTTON, INPUT);
    // pinMode(ALARM_BUTTON, INPUT);
    // attachInterrupt(digitalPinToInterrupt(NEXT_BUTTON), nextButtonISR, RISING);
    // attachInterrupt(digitalPinToInterrupt(PRE_BUTTON), preButtonISR, RISING);
    // attachInterrupt(digitalPinToInterrupt(ALARM_BUTTON), alarmButtonISR, RISING);
}
void loop()
{
//    randomData();
//    j1939.receivedData();
  j1939.transmitData();
//   j1939.dataProcessing();
//    hmi->setParameterValue(j1939.parameter_value);
//    hmi->loop();
}

void nextButtonISR()
{
    if (isTimeOutButton())
    {
        currentPage++;
        if (currentPage > MAX_PAGE)
        {
            currentPage = MAX_PAGE;
        }
        hmi->setPage(currentPage);
    }
}
void preButtonISR()
{

    if (isTimeOutButton())
    {
        currentPage--;
        if (currentPage < 0)
        {
            currentPage = 0;
        }
        hmi->setPage(currentPage);
    }
}

void alarmButtonISR()
{
    if (isTimeOutButton())
    {
        Serial.println("ALARM");
    }
}

bool buttonIsPress(bool button_pin)
{
    Serial.println(digitalRead(button_pin));
    if (!digitalRead(button_pin))
    {
        unsigned long timeout = millis();
        while (!digitalRead(button_pin))
        {
            if (millis() - timeout >= 200)
            {
                break;
            }
        }
        return true;
    }
    else
    {
        return false;
    }
}

bool isTimeOutButton()
{
    if (millis() - timeCountButton >= 200)
    {
        timeCountButton = millis();
        return true;
    }
    else
    {
        return false;
    }
}

void randomData()
{
    parameter_value.coolant_temp=random(0,120);
    parameter_value.oil_press=random(0,700);
    parameter_value.xmsn_temp=random(0,120);
    parameter_value.xmsn_pres=random(0,120);
    parameter_value.boost_pres=random(0,350);
    parameter_value.engine_load=random(0,100);
    parameter_value.fuel_rate=random(0,300);
    parameter_value.speed=random(0,9999);
    parameter_value.fuel_diff_pres=random(0,500);
    parameter_value.fuel_pres=random(0,700);
    parameter_value.battery=random(20,24);
    parameter_value.mainiford_temp=random(0,200);
    parameter_value.fuel_temp=random(0,100);
    parameter_value.engine_hours=random(0,999);

    parameter_value.trip_engine_hours=random(0,9999);
    parameter_value.trip_idle_hours=random(0,9999);
    parameter_value.trip_fuel=random(0,9999);
    parameter_value.trip_idle_fuel=random(0,9999);
    parameter_value.avg_fuel_consumption=random(0,9999);

    parameter_value.lifetime_engine_hours=random(0,9999);
    parameter_value.lifetime_idle_hours=random(0,9999);
    parameter_value.lifetime_fuel=random(0,9999);
    parameter_value.lifetime_idle_fuel=random(0,9999);
    parameter_value.lifrtime_avg_fuel_consumpiton=random(0,9999);

}
