#include "Arduino.h"
#include "WiFi.h"
#include "common.h"
#include "HardwareSerial.h"
#include "LCD_CLASS/HMI_DWIN.h"
#include "SAE1939.h"


SAE1939 j1939;

HardwareSerial uart_dwin(1);
HMI_DWIN *hmi=new HMI_DWIN(&uart_dwin);
int currentPage=0;

unsigned long timeCountButton;

parameter_value_struct_t parameter_value;

void nextButtonISR();
void preButtonISR();
void alarmButtonISR();
void setup();
void loop();
bool buttonIsPress(bool button_pin);
bool isTimeOutButton();
void randomData();