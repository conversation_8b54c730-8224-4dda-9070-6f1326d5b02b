#include "GatewaySolar.h"
void setup()
{
    delay(5000);
    Serial.begin(115200);
    if (!SPIFFS.begin())
    {
        Serial.println("SPIFFS Mount Failed");
        return;
    }
    DeviceInfor.init();
    eth->init(IPAddress(192, 168, 1, 112), IPAddress(192, 168, 1, 1), IPA<PERSON><PERSON>(255, 255, 255, 0), IPAdd<PERSON>(8, 8, 8, 8), IPAddress(8, 8, 4, 4));
    // NetworkService.init();
    // SmartConfig.init();
    // ParingApiServer.init();
    ThingsboardService.init(on_message);
    ThingsboardService.connectMqtt();
    ThingsboardOTA.setDeviceToken(ThingsboardService.getDeviceTokent());
    Serial.println(ThingsboardService.getDeviceTokent());
    ThingsboardOTA.init();

    esp_task_wdt_init(10, true); // Timeout of 10 seconds
    mb->client();
}
void loop()
{
    // NetworkService.loop();
    // SmartConfig.loop();
    // initStatusUpdate();
    ThingsboardService.loop();
    ThingsboardOTA.loop();
    if (ThingsboardService.needReconnect())
    {

        ThingsboardService.connectMqtt();
    }
    // inveter_1->loop();
    // inveter_2->loop();
    // inveter_3->loop();

    // updateTelemetry();
    // updateInverterState();

    // esp_task_wdt_reset();
}

void initStatusUpdate()
{
    static bool bit_first_update = false;

    if (bit_first_update == false)
    {
        if (ThingsboardService.mqttIsConnected())
        {
            for (int i = 0; i < 17; i++)
            {
                if (i < 4)
                {
                    String key = "power_" + (String)i;
                    ThingsboardService.sendTelemertry(key, inveter_1->_currentState[i]);
                }

                if ((i > 3) && (i < 12))
                {
                    String key = "power_" + (String)i;
                    ThingsboardService.sendTelemertry(key, inveter_2->_currentState[i - 4]);
                }
                if ((i > 11) && (i < 17))
                {
                    String key = "power_" + (String)i;
                    ThingsboardService.sendTelemertry(key, inveter_3->_currentState[i - 12]);
                }
            }

            for (int i = 0; i < 17; i++)
            {
                if (i < 4)
                {
                    String key = "inv_state_" + (String)i;
                    ThingsboardService.sendTelemertry(key, inveter_1->getInverterState(inveter_1->device_status[i]));
                }
                if ((i > 3) && (i < 12))
                {
                    String key = "inv_state_" + (String)i;
                    ThingsboardService.sendTelemertry(key, inveter_2->getInverterState(inveter_2->device_status[i - 4]));
                }
                if ((i > 11) && (i < 17))
                {
                    String key = "inv_state_" + (String)i;
                    ThingsboardService.sendTelemertry(key, inveter_3->getInverterState(inveter_3->device_status[i - 12]));
                }
            }

            bit_first_update = true; // k cap nhat nua
        }
    }
}

void updateTelemetry()
{
    static long preUpdateTele = millis();
    if (millis() - preUpdateTele >= 230)
    {
        preUpdateTele = millis();
        for (int i = 0; i < 17; i++)
        {
            if (i < 4)
            {
                String key = "power_" + (String)i;
                if (inveter_1->_newState[i] != inveter_1->_currentState[i])
                {
                    inveter_1->writeStatePowerInveter(inveter_1->_newState[i], i);
                    ThingsboardService.sendTelemertry(key, inveter_1->_newState[i]);
                }
            }
            if ((i > 3) && (i < 12))
            {
                String key = "power_" + (String)i;
                if (inveter_2->_newState[i - 4] != inveter_2->_currentState[i - 4])
                {
                    inveter_2->writeStatePowerInveter(inveter_2->_newState[i - 4], i - 4);
                    ThingsboardService.sendTelemertry(key, inveter_2->_currentState[i - 4]);
                }
            }
            if ((i > 11) && (i < 17))
            {
                String key = "power_" + (String)i;

                if (inveter_3->_newState[i - 12] != inveter_3->_currentState[i - 12])
                {
                    inveter_3->writeStatePowerInveter(inveter_3->_newState[i - 12], i - 12);
                    ThingsboardService.sendTelemertry(key, inveter_3->_currentState[i - 12]);
                }
            }
        }
    }
}

void updateInverterState()
{
    static long preUpdateState = millis();
    if (millis() - preUpdateState >= 500)
    {
        preUpdateState = millis();
        for (int i = 0; i < 17; i++)
        {
            if (i < 4)
            {
                String key = "inv_state_" + (String)i;
                if (inveter_1->device_status[i] != inveter_1->pre_device_status[i])
                {
                    inveter_1->pre_device_status[i] = inveter_1->device_status[i];
                    ThingsboardService.sendTelemertry(key, inveter_1->getInverterState(inveter_1->device_status[i]));
                }
            }
            if ((i > 3) && (i < 12))
            {
                String key = "inv_state_" + (String)i;
                if (inveter_2->device_status[i - 4] != inveter_2->pre_device_status[i - 4])
                {
                    inveter_2->pre_device_status[i - 4] = inveter_2->device_status[i - 4];
                    ThingsboardService.sendTelemertry(key, inveter_2->getInverterState(inveter_2->device_status[i - 4]));
                }
            }
            if ((i > 11) && (i < 17))
            {
                String key = "inv_state_" + (String)i;
                if (inveter_3->device_status[i - 12] != inveter_3->pre_device_status[i - 12])
                {
                    inveter_3->pre_device_status[i - 12] = inveter_3->device_status[i - 12];
                    ThingsboardService.sendTelemertry(key, inveter_3->getInverterState(inveter_3->device_status[i - 12]));
                }
            }
        }
    }
}

void on_message(char *topic, uint8_t *payload, unsigned int length)
{
    DB_PRINTLN("On message");
    StaticJsonDocument<1024> doc;
    char json[length + 1];
    strncpy(json, (char *)payload, length);
    json[length] = '\0';
    DB_PRINTLN("TOPIC: " + (String)topic);
    DB_PRINTLN("Message: " + (String)json);
    DeserializationError error = deserializeJson(doc, json);
    if (error)
    {
        DB_PRINTLN("deserializeJson failed");
        DB_PRINTLN(error.f_str());
        return;
    }
    if (strstr((char *)payload, "set_state") != NULL)
    {
        for (int i = 0; i < 17; i++)
        {
            if (i < 4)
            {
                String key = "power_" + (String)i;
                if (doc["params"].containsKey(key))
                {
                    bool state = doc["params"][key].as<bool>();
                    inveter_1->setPower(state, i);
                    ThingsboardService.sendTelemertry(key, state);
                }
            }

            if ((i > 3) && (i < 12))
            {
                String key = "power_" + (String)i;
                if (doc["params"].containsKey(key))
                {
                    bool state = doc["params"][key].as<bool>();
                    inveter_2->setPower(state, i - 4);
                    ThingsboardService.sendTelemertry(key, state);
                }
            }
            if ((i > 11) && (i < 17))
            {
                String key = "power_" + (String)i;
                if (doc["params"].containsKey(key))
                {
                    bool state = doc["params"][key].as<bool>();
                    inveter_3->setPower(state, i - 12);
                    ThingsboardService.sendTelemertry(key, state);
                }
            }
        }
    }
    if (strstr((char *)payload, "update_schedule") != NULL)
    {
    }
    if (doc.containsKey("fw_title"))
    {
        Serial.println("Checking new firmware......");
        OTA_status_check_t otaStatus = ThingsboardOTA.checkFirmwareVersion();
        if (otaStatus == NEW_FIRM_AVAILBLE)
        {
            ThingsboardOTA.executeOTA();
        }
    }

    String responseTopic = String(topic);
    responseTopic.replace("request", "response");
    DB_PRINTLN(responseTopic.c_str());
}