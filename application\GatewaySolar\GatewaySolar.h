#include "SmartConfig.h"
#include "ParingApiServer.h"
#include "NetworkService.h"
#include "DeviceInfor.h"
#include "ThingsboardService.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"
#include "OTAService.h"
#include "InveterClass/InveterSolar.h"
#include "ML_Ethernet.h"
#include "MLFs.h"
#include "ModbusIP_ESP8266.h"

#include "esp_task_wdt.h"

ModbusIP *mb = new ModbusIP();
ML_Ethernet *eth = new ML_Ethernet();
hw_timer_t *Timer_1s = NULL;
InveterSolar *inveter_1 = new InveterSolar(mb, IPAddress(192, 168, 1, 150),4);  // dong minh
InveterSolar *inveter_2 = new InveterSolar(mb, IPAddress(192, 168, 1, 151),8);  // gia nguyen
InveterSolar *inveter_3 = new InveterSolar(mb, IPAddress(192, 168, 1, 147),5);  // hung hai son


void on_message(char *topic, uint8_t *payload, unsigned int length);
void updateTelemetry();
void IRAM_ATTR Timer0_ISR()
{
}
void timer1sInit()
{
    Timer_1s = timerBegin(0, 80, true);
    timerAttachInterrupt(Timer_1s, &Timer0_ISR, true);
    timerAlarmWrite(Timer_1s, 1000000, true);
    timerAlarmEnable(Timer_1s);
}

void initStatusUpdate();
void setup();
void loop();

void updateInverterState();