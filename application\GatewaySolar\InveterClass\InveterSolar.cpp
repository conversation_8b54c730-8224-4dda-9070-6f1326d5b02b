#include "InveterSolar.h"

void InveterSolar::writeStatePowerInveter(bool state, int unitID)
{

    if (state != _currentState[unitID])
    {
        if (state)
        {
            _mbIP->writeHreg(_remote, REG_STATR, TURN_ON, NULL, unitID + 1);
        }
        else
        {
            _mbIP->writeHreg(_remote, REG_STATR, TURN_OFF, NULL, unitID + 1);
        }
    }
    _mbIP->task();
}

bool InveterSolar::readStatePowerInveter(int unitID)
{
    _mbIP->readHreg(_remote, REG_STATR, &modbus_value[unitID], 1, NULL, unitID + 1);

    // Serial.println("value  " + (String)unitID + " = " + (String)modbus_value[unitID] );

    _mbIP->task();

    if (modbus_value[unitID] == TURN_ON)
    {
        _currentState[unitID] = true;
        return true;
    }
    if (modbus_value[unitID] == TURN_OFF)
    {
        _currentState[unitID] = false;
        return false;
    }
    else
    {
        return false;
    }
}

InveterSolar::InveterSolar(ModbusIP *mbIP, IPAddress remote, uint8_t maxSlave)
{
    _maxSlave = maxSlave;
    _mbIP = mbIP;
    _remote = remote;
}

void InveterSolar::loop()
{
    static long preTime = millis();
    if (millis() - preTime >= 100)
    {
        preTime = millis();
        if (_mbIP->isConnected(_remote))
        {
            for (int i = 0; i < _maxSlave; i++)
            {
                readDeviceStatus(i);
                readStatePowerInveter(i);
            }
        }
        else
        {
            _mbIP->connect(_remote);
        }
    }

    _mbIP->task();
}

void InveterSolar::readDeviceStatus(int unitID)
{
    _mbIP->readIreg(_remote, REG_DEVICE_STATE, &device_status[unitID], 1, NULL, unitID + 1);
    // Serial.println("device_status "  + (String)unitID + " = " + (String)device_status[unitID] );
    _mbIP->task();
}

void InveterSolar::setPower(bool state, int unitID)
{
    _newState[unitID] = state;
}

String InveterSolar::getInverterState(int value)
{
    switch (value)
    {
    case 0:

        break;
    case 32768: // stop  0x8000
        return "stop";
        break;
    case 4864: // stop  0x1300
        return "key stop";
        break;
    case 5376: // stop  0x1500
        return "Emergency stop";
        break;
    case 5120: // standby
        return "standby";
        break;
    case 4608: // initial standby 0x1200
        return "initial standby";
        break;
    case 5632: //  starting 0x1600
        return "starting";
        break;
    case 37120: //  0x9100
        return "alarm run";
        break;
    case 33024: //  0x8100
        return "Derating run";
        break;
    case 33280: //  0x8200
        return "Dispatch run";
        break;
    case 21760: //  0x5500
        return "Fault run";
        break;
    case 9472: //  0x2500
        return "Communicate fault";
        break;
    default:
        break;
    }
}

InveterSolar::~InveterSolar()
{
}