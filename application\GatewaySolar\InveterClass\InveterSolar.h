#include "Arduino.h"
#include "ModbusIP_ESP8266.h"
#define REG_POWER 5031   // to check to cong suat of function 04
#define REG_STATR 5005      // to turn off or on  : function 03 holding
#define REG_DEVICE_STATE 5037  // INPUT REGISTER - THANH GHI TRANG THAI INVERTER  
#define TURN_OFF 206
#define TURN_ON 207

class InveterSolar
{
private:
    IPAddress _remote;
    ModbusIP *_mbIP;
    uint8_t _maxSlave = 5;
    uint16_t modbus_value[10] = {0};
    // uint16_t device_status[10] = {0};
public:
    bool _newState[10] = {true};
    bool _currentState[10] = {true};
    uint16_t device_status[10] = {0};
    uint16_t pre_device_status[10] = {0};

    InveterSolar(ModbusIP *mbIP, IPAddress remote, uint8_t maxSlave);
    void loop();
    void readDeviceStatus(int unitID);
    bool readStatePowerInveter(int unitID);
    void writeStatePowerInveter(bool state, int unitID);
    void setPower(bool state, int unitID);
    uint8_t getNumberSlave()
    {
        return _maxSlave;
    }

    String getInverterState(int value);

    ~InveterSolar();
};