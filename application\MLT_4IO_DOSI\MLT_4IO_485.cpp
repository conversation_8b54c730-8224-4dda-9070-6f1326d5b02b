#include "MLT_4IO_485.h"
#include "MLT_EC_PH.h"

MltEcPhSensor sensor;

void setup()
{
    Serial.begin(9600);
    Serial2.begin(9600);
    sensor.initialize();
    board8io->begin();
    mb->begin(&Serial2);
    mb->slave(SLAVE_ID);
    mb->addHreg(0, 0, 50);
    mb->addIreg(0, 0, 50);
    mb->addCoil(0, 0, 50);
}

void loop()
{
    sensor.loop();
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], mb->Coil(i));
    }
    UpdateValue();
    GetDataSensor();
    UpdateSensor();
    if (mb->Coil(30))
    {
        LogicEC();
    }
    LogicPH();
    // Printdosing();
    CalibSensor();
    mb->task();
    yield();
}

void UpdateValue()
{
    dosi_EC.setTds = mb->Hreg(0);
    dosi_EC.dosingTime_Tds = mb->Hreg(2);
    dosi_EC.waitTime_Tds = mb->Hreg(1);
    dosi_EC.ratio_A = mb->Hreg(3);
    dosi_EC.ratio_B = mb->Hreg(4);
    dosi_EC.alarm_Tds_max = mb->Hreg(9);
    dosi_EC.alarm_Tds_min = mb->Hreg(10);

    dosi_PH.setPh = mb->Hreg(5);
    dosi_PH.dosingTime_Ph = mb->Hreg(6);
    dosi_PH.waitTime_Ph = mb->Hreg(7);
    dosi_PH.alarm_Ph_max = mb->Hreg(11);
    dosi_PH.alarm_Ph_min = mb->Hreg(12);
    calib.EC = mb->Hreg(20);
}

void UpdateSensor()
{
    mb->Ireg(0, GetSS.Tds);
    mb->Ireg(1, GetSS.Ph);
    mb->Ireg(2, GetSS.Temp);
    mb->Ireg(3, GetSS.Ec);
}

void GetDataSensor()
{
    static long pretime = millis();
    if (millis() - pretime >= 1000)
    {
        pretime = millis();
        GetSS.Ec = sensor.getEcValue();
        GetSS.Tds = sensor.getEcValue() * 0.5;
        GetSS.Ph = sensor.getPhValue() * 100;
        GetSS.Temp = sensor.getTemperature() * 100;
    }
}

void LogicEC()
{
    static unsigned long ecDosingStartTime = 0;
    static bool isECDosing = false;
    static bool isWaiting = false;
    int Timedosi = dosi_EC.dosingTime_Tds * 1000;
    int WaitTime = dosi_EC.waitTime_Tds * 1000;
    if (GetSS.Tds < dosi_EC.setTds)
    {
        if (!isECDosing && !isWaiting)
        {
            ecDosingStartTime = millis();
            isECDosing = true;
            mb->Coil(0, HIGH);
            mb->Coil(1, HIGH);
        }
        else if (isECDosing && (millis() - ecDosingStartTime >= Timedosi))
        {
            mb->Coil(0, LOW);
            mb->Coil(1, LOW);
            isECDosing = false;
            isWaiting = true;
            ecDosingStartTime = millis();
        }
        else if (isWaiting && (millis() - ecDosingStartTime >= WaitTime))
        {
            isWaiting = false;
        }
    }
    else
    {
        mb->Coil(0, LOW);
        mb->Coil(1, LOW);
        isECDosing = false;
        isWaiting = false;
    }
}
void LogicPH()
{
}

void Printdosing()
{
    static long delePrint = millis();
    if (millis() - delePrint >= 1000)
    {
        delePrint = millis();
        Serial.print("TDS: ");
        Serial.println(GetSS.Tds);
        Serial.print("PH: ");
        Serial.print(GetSS.Ph);
        Serial.println(" ");
        Serial.print("TEMP: ");
        Serial.print(GetSS.Temp);
        Serial.println(" *C");
    }
}
void CalibSensor()
{
    bool ecCalibCurrentState = mb->Coil(20);
    if (ecCalibCurrentState && !ecCalibPreviousState)
    {
        sendEcCalibValue();
    }
    ecCalibPreviousState = ecCalibCurrentState;

    bool phCalibCurrentState4 = mb->Coil(21);
    if (phCalibCurrentState4 && !phCalibPreviousState4)
    {
        sendPhCalibValue(4);
    }
    phCalibPreviousState4 = phCalibCurrentState4;

    bool phCalibCurrentState7 = mb->Coil(22);
    if (phCalibCurrentState7 && !phCalibPreviousState7)
    {
        sendPhCalibValue(7);
    }
    phCalibPreviousState7 = phCalibCurrentState7;
}
void sendEcCalibValue()
{
    String jsonRequest = "{\"ec_calib\":" + String(calib.EC) + "}";
    sensor.sendRequest(jsonRequest);
}

void sendPhCalibValue(int value)
{
    String jsonRequest = "{\"ph_calib\":" + String(value) + "}";
    sensor.sendRequest(jsonRequest);
    Serial.print("Đã gửi chuỗi: ");
    Serial.println(jsonRequest);
}
