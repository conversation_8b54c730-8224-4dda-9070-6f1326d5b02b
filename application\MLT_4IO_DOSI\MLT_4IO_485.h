#include "Arduino.h"
#include "MLBoard8CH.h"
#include "ModbusRTU.h"

#define SLAVE_ID 2

ModbusRTU *mb = new ModbusRTU();
MLBoard8CH *board8io = new MLBoard8CH();
bool ecCalibPreviousState = false;
bool phCalibPreviousState4 = false;
bool phCalibPreviousState7 = false;

void setup();
void loop();
void UpdateValue();
void Printdosing();
void UpdateSensor();
void GetDataSensor();
void LogicEC();
void LogicPH();
void CalibSensor();
void sendEcCalibValue();
void sendPhCalibValue(int value);

struct DosingParameters
{
    float setTds;
    unsigned long dosingTime_Tds;
    unsigned long waitTime_Tds;
    int ratio_A;
    int ratio_B;
    float alarm_Tds_max;
    float alarm_Tds_min;
};
DosingParameters dosi_EC;

struct DosingPH
{
    float setPh;
    unsigned long dosingTime_Ph;
    unsigned long waitTime_Ph;
    float alarm_Ph_max;
    float alarm_Ph_min;
};
DosingPH dosi_PH;

struct Sensor_EC
{
    float Tds;
    float Ec;
    float Ph;
    float Temp;
};
Sensor_EC GetSS;

struct Calib_Sensor
{
    float EC;
    float Ph;
    float Temp;
};
Calib_Sensor calib;
