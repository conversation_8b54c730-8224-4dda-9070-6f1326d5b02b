#include "MLT_EC_PH.h"
#include <ArduinoJson.h>

// Constructor
MltEcPhSensor::MltEcPhSensor() {
    sensor_uart = new HardwareSerial(1); // Sử dụng UART2
    ph_value = 0.0;
    ec_value = 0.0;
    temperature = 0.0;
    dataAvailable = false;
    lastRequestTime = 0;
}

void MltEcPhSensor::initialize() {
    sensor_uart->begin(9600, SERIAL_8N1, SENSOR_RX, SENSOR_TX); // Khởi động UART cho cảm biến
    sensor_uart->setTimeout(50); // Đặt thời gian chờ cho UART
}

void MltEcPhSensor::sendRequest(const String &request) {
    sensor_uart->println(request); // Gửi yêu cầu đến cảm biến
#ifdef DEBUG_SENSOR
    Serial.println("Gửi yêu cầu: " + request);
#endif
}

void MltEcPhSensor::handleUartEvent() {
    if (sensor_uart->available()) {
        String jsonString = sensor_uart->readString(); // Đọc chuỗi JSON từ UART
#ifdef DEBUG_SENSOR
        Serial.print("Chuỗi giá trị nhận được: ");
        Serial.println(jsonString);
#endif
        parseJson(jsonString); // Phân tích chuỗi JSON
    }
}

void MltEcPhSensor::parseJson(const String &jsonString) {
    StaticJsonDocument<1024> doc;
    DeserializationError error = deserializeJson(doc, jsonString);

    if (error) {
#ifdef DEBUG_SENSOR
        Serial.print("deserializeJson() thất bại: ");
        Serial.println(error.c_str());
#endif
        return;
    }
    ec_value = doc["sensors"]["ec_sensor"];       // Lấy giá trị EC từ JSON
    ph_value = doc["sensors"]["ph_sensor"];       // Lấy giá trị pH từ JSON
    temperature = doc["sensors"]["temp_sensor"];  // Lấy giá trị nhiệt độ từ JSON
    dataAvailable = true; // Đánh dấu dữ liệu đã có sẵn
}

void MltEcPhSensor::loop() {
    unsigned long currentTime = millis();
    if (currentTime - lastRequestTime >= 1000) { // Gửi yêu cầu mỗi giây
        lastRequestTime = currentTime;
        sendRequest("{\"get_sensors\":1}");
    }
    handleUartEvent(); // Xử lý sự kiện UART

    if (dataAvailable) {
        float ph = getPhValue();     // Lấy giá trị pH
        float ec = getEcValue();     // Lấy giá trị EC
        float temp = getTemperature(); // Lấy giá trị nhiệt độ

#ifdef DEBUG_SENSOR
        if (!isnan(ph)) {
            // Serial.print("Giá trị pH: ");
            // Serial.println(ph);
        }
        if (!isnan(ec)) {
            // Serial.print("Giá trị EC: ");
            // Serial.println(ec);
        }
        if (!isnan(temp)) {
            // Serial.print("Giá trị nhiệt độ: ");
            // Serial.println(temp);
        }
#endif
    }
}

float MltEcPhSensor::getPhValue() {
    if (dataAvailable= true) {
        dataAvailable = false; // Đặt lại cờ
        return ph_value;
    } else {
        return NAN; // Trả về Not-a-Number nếu không có dữ liệu
    }
}

float MltEcPhSensor::getEcValue() {
    if (dataAvailable= true) {
        dataAvailable = false; // Đặt lại cờ
        return ec_value;
    } else {
        return NAN; // Trả về Not-a-Number nếu không có dữ liệu
    }
}

float MltEcPhSensor::getTemperature() {
    if (dataAvailable= true) {
        dataAvailable = false; // Đặt lại cờ
        return temperature;
    } else {
        return NAN; // Trả về Not-a-Number nếu không có dữ liệu
    }
}
