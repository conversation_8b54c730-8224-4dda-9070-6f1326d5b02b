#ifndef MLT_ECPHSENSOR_H
#define MLT_ECPHSENSOR_H

#include "Arduino.h"
#include "HardwareSerial.h"

#define SENSOR_TX 35 // Chân TX của cảm biến
#define SENSOR_RX 34 // Chân RX của cảm biến

#define DEBUG_SENSOR // Định nghĩa cho việc debug cảm biến

class MltEcPhSensor {
public:
    MltEcPhSensor(); // Constructor

    void initialize(); // Phương thức khởi tạo cảm biến
    void sendRequest(const String &request); // Phương thức gửi yêu cầu đến cảm biến
    void handleUartEvent(); // Phương thức xử lý dữ liệu nhận từ UART
    void loop(); // Phương thức xử lý các hoạt động của cảm biến trong vòng lặp chính

    float getPhValue(); // Phương thức lấy giá trị pH cuối cùng
    float getEcValue(); // Phương thức lấy giá trị EC cuối cùng
    float getTemperature(); // Phương thức lấy giá trị nhiệt độ cuối cùng

private:
    HardwareSerial* sensor_uart;
    float ph_value;
    float ec_value;
    float temperature;
    bool dataAvailable;
    unsigned long lastRequestTime;

    void parseJson(const String &jsonString); // Phương thức phân tích dữ liệu JSON
};

#endif // MLT_ECPHSENSOR_H
