#include "MLT_8IO_CONTROL_VIIS.h"

void setup()
{
    delay(5000);
    timer1sInit();
    Serial.begin(115200);
    if (!SPIFFS.begin())
    {
        Serial.println("SPIFFS Mount Failed");
        return;
    }
    DeviceInfor.init();
    NetworkService.init();
    SmartConfig.init();
    ParingApiServer.init();
    ThingsboardService.init(on_message);
    ThingsboardService.connectMqtt();
    ThingsboardOTA.setDeviceToken(ThingsboardService.getDeviceTokent());
    ThingsboardOTA.init();
    ml_board->begin();
    ntp->begin();
}
void loop()
{
    static unsigned long timeUpdate = millis();
    // if (millis() - timeUpdate > timeUpdateRTC)
    // {
    //     if (ntp->syncTime())
    //     {
    //         ntp->printLocalTime();
    //         ml_board->adjustTime(ntp->getEpochTime());
    //         DB_PRINTLN("updated RTC");
    //         timeUpdateRTC = 1000 * 60 * 30;
    //     }
    //     else
    //     {
    //         DB_PRINTLN("RTC update fail");
    //     }
    //     timeUpdate = millis();
    // }
    NetworkService.loop();
    SmartConfig.loop();
    ThingsboardService.loop();
    if (ThingsboardService.needReconnect())
    {

        ThingsboardService.connectMqtt();
    }
    if (ThingsboardService.getEventConnected())
    {
        updateThingsboardState();
        ThingsboardService.removeEventConncted();
    }
    if (timer_count.timer_for_schedule == 0)
    {
        timer_count.timer_for_schedule = 1;
        // updateShedule();
    }
    if (timer_count.timer_update_in_out == 0)
    {
        timer_count.timer_update_in_out = 1;
        updateThingsboardByEvent();
    }
}
void timer1sInit()
{
    Timer_1s = timerBegin(0, 80, true);
    timerAttachInterrupt(Timer_1s, &Timer0_ISR, true);
    timerAlarmWrite(Timer_1s, 1000000, true);
    timerAlarmEnable(Timer_1s);
}
void on_message(char *topic, uint8_t *payload, unsigned int length)
{
    DB_PRINTLN("On message");
    StaticJsonDocument<1024> doc;
    char json[length + 1];
    strncpy(json, (char *)payload, length);
    json[length] = '\0';
    DB_PRINTLN("TOPIC: " + (String)topic);
    DB_PRINTLN("Message: " + (String)json);
    DeserializationError error = deserializeJson(doc, json);
    if (error)
    {
        DB_PRINTLN("deserializeJson failed");
        DB_PRINTLN(error.f_str());
        return;
    }
    if (strstr((char *)payload, "set_state") != NULL)
    {
        for (int i = 0; i < NUM_RL; i++)
        {
            String key = "power_" + (String)i;
            if (doc["params"].containsKey(key))
            {
                bool state = doc["params"][key].as<bool>();
                digitalWrite(output[i], state);
                pre_output[i] = state;
                ThingsboardService.sendTelemertry(key, (bool)digitalRead(output[i]));
            }
        }
    }
    if (strstr((char *)payload, "update_schedule") != NULL)
    {
        Schedule.httpGETRequest();
    }
        if (doc.containsKey("fw_title"))
    {
        Serial.println("Checking new firmware......");
        OTA_status_check_t otaStatus = ThingsboardOTA.checkFirmwareVersion();
        if (otaStatus == NEW_FIRM_AVAILBLE)
        {
            ThingsboardOTA.executeOTA();
        }
    }

    String responseTopic = String(topic);
    responseTopic.replace("request", "response");
    DB_PRINTLN(responseTopic.c_str());
}
void updateInOut()
{
    for (int i = 0; i < 8; i++)
    {
        String key = "input_" + (String)i;
        String key_pw = "power_" + (String)i;
        if (pre_input[i] != (bool)digitalRead(input[i]))
        {
            pre_input[i] = (bool)digitalRead(input[i]);
            ThingsboardService.sendTelemertry(key, pre_input[i]);
        }
        if (pre_output[i] != (bool)digitalRead(output[i]))
        {
            pre_output[i] = (bool)digitalRead(output[i]);
            ThingsboardService.sendTelemertry(key_pw, pre_output[i]);
        }
    }
}
void updateShedule()
{
    TSchedule m_schedule;
    Serial.println(ml_board->getTimeString());

    for (int i = 0; i < Schedule.getNumberOfSchedule(); i++)
    {
        m_schedule = Schedule.getSchedule(i);
        if (m_schedule.time == ml_board->getTimeString() && m_schedule.enable == true && strstr(m_schedule.interval.c_str(), ml_board->getStringDayOfWeek().c_str()))
        {
            timer_count.timer_for_schedule = 60;
            for (int j = 0; j < m_schedule.action_count; j++)
            {
                for (int k = 0; k < NUM_RL; k++)
                {
                    String key = "power_" + (String)k;
                    if (m_schedule.action[j].key == key)
                    {
                        bool state = false;
                        m_schedule.action[j].value == "true" ? state = true : state = false;
                        digitalWrite(output[k], state);
                    }
                }
            }
        }
    }
}
void updateThingsboardState()
{
    DynamicJsonDocument data(512);
    data["local_ip"] = WiFi.localIP().toString();
    for (int i = 0; i < NUM_RL; i++)
    {
        String key = "power_" + (String)i;
        String key_in = "input_" + (String)i;
        data[key] = digitalRead(output[i]);
        data[key_in] = digitalRead(input[i]);
    }
    String objectString;
    serializeJson(data, objectString);
    ThingsboardService.sendTelemertry(objectString);
}
void updateThingsboardByEvent()
{
    DynamicJsonDocument data(512);
    for (int i = 0; i < 8; i++)
    {
        String key = "input_" + (String)i;
        String key_pw = "power_" + (String)i;
        if (pre_input[i] != (bool)digitalRead(input[i]))
        {
            pre_input[i] = (bool)digitalRead(input[i]);
            data[key] = pre_input[i];
        }
        if (pre_output[i] != (bool)digitalRead(output[i]))
        {
            pre_output[i] = (bool)digitalRead(output[i]);
            data[key_pw] = pre_output[i];
        }
    }
    String objectString;
    serializeJson(data, objectString);
    if (objectString.length() > 5)
    {
        ThingsboardService.sendTelemertry(objectString);
    }
}