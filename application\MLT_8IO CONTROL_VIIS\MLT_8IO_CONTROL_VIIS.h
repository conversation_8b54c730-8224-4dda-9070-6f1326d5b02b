#include "MLBoard8CH.h"
#include "SmartConfig.h"
#include "ParingApiServer.h"
#include "NetworkService.h"
#include "DeviceInfor.h"
#include "ThingsboardService.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"
#include "OTAService.h"
#include "ScheduleService.h"
#include "ML_NTP.h"
#include "OTAService.h"
#define NUM_RL 8
#define TIME_UPDATE_SCHEDULE 1
#define TIME_UPDATE_IN_OUT 1

bool pre_input[8] = {false};
bool pre_output[8] = {false};

ML_NTP *ntp = new ML_NTP();
MLBoard8CH *ml_board = new MLBoard8CH();
hw_timer_t *Timer_1s = NULL;

unsigned long timeUpdateRTC=1000;
typedef struct
{
    int timer_for_schedule = 0;
    int timer_update_in_out = 0;
} timer_count_t;

timer_count_t timer_count;
void IRAM_ATTR Timer0_ISR()
{
    if (timer_count.timer_for_schedule > 0)
        timer_count.timer_for_schedule--;
    if (timer_count.timer_update_in_out > 0)
        timer_count.timer_update_in_out--;
}
void timer1sInit();
void on_message(char *topic, uint8_t *payload, unsigned int length);
void updateInOut();
void updateThingsboardState();
void updateThingsboardByEvent();
void updateShedule();

void setup();
void loop();