#include "MLT_8IO_485.h"
#include "MLT_EC_PH.h"
#include <WiFi.h> // Thêm thư viện WiFi

MltEcPhSensor sensor;
// <PERSON><PERSON> báo thông tin WiFi
const char *ssid = "Thoc";
const char *password = "23011984";
// Thiết lập IP tĩnh
IPAddress local_IP(192, 168, 1, 159); // Địa chỉ IP tĩnh cho ESP32 ///159 dosi,162 van
IPAddress gateway(192, 168, 1, 1);    // Địa chỉ gateway (thường là địa chỉ của router)
IPAddress subnet(255, 255, 255, 0);   // Subnet mask
IPAddress primaryDNS(8, 8, 8, 8);     // DNS chính
IPAddress secondaryDNS(8, 8, 4, 4);   // DNS phụ (có thể tùy chọn)

void connectToWiFi()
{
    Serial.println(ssid);
    Serial.println(password);

    Serial.print("Đang kết nối WiFi...");

    if (!WiFi.config(local_IP, gateway, subnet, primaryDNS, secondaryDNS))
    {
        Serial.println("Cấu hình IP tĩnh thất bại!");
    }
    WiFi.begin(ssid, password);

    while (WiFi.status() != WL_CONNECTED)
    {
        delay(1000);
        Serial.print(".");
    }

    Serial.println("");
    Serial.println("Đã kết nối WiFi!");
    Serial.print("Địa chỉ IP: ");
    Serial.println(WiFi.localIP());
}
void setup()
{
    Serial.begin(9600);
    Serial2.begin(9600);
    sensor.initialize();
    board8io->begin();
    rtu->begin(&Serial2);
    rtu->slave(SLAVE_ID);
    mb.addHreg(0, 0, 50);
    mb.addIreg(0, 0, 50);
    mb.addCoil(0, 0, 50);

    connectToWiFi(); // Gọi hàm kết nối WiFi
    mb.server();
}

bool previousCoil30State = false; // Lưu trạng thái trước đó của Coil(30)
bool previousCoil31State = false; // Lưu trạng thái trước đó của Coil(30)

void loop()
{
    reconnectWiFi(); // Kiểm tra và kết nối lại WiFi nếu mất kết nối

    sensor.loop();

    // Điều khiển 8 relay từ 0 đến 7 theo trạng thái coil
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], mb.Coil(i));
    }
    // Đọc và in trạng thái của các ngõ vào (input)
    for (int i = 0; i < 8; i++)
    {
        int inputState = digitalRead(input[i]); // Đọc trạng thái của ngõ vào
    }

    UpdateValue();
    GetDataSensor();
    UpdateSensor();

    bool currentCoil30State = mb.Coil(30); // Trạng thái hiện tại của Coil(30)

    // Kiểm tra nếu Coil(30) chuyển từ 1 sang 0
    if (previousCoil30State && !currentCoil30State)
    {
        // Nếu Coil(30) vừa chuyển từ 1 sang 0 thì tắt Coil(0) và Coil(1)
        Serial.println("Coil(30) chuyển từ 1 về 0, tắt Coil(0) và Coil(1)");
        mb.Coil(0, LOW); // Tắt Coil(0)
        mb.Coil(1, LOW); // Tắt Coil(1)
    }

    // Nếu Coil(30) đang ở trạng thái 1 thì chạy chương trình LogicEC
    if (currentCoil30State)
    {
        LogicEC(); // Chạy chương trình LogicEC khi mb.Coil(30) = 1
    }
    ///////////////////////////////////////////////////////////////////////////////////////
    bool currentCoil31State = mb.Coil(31); // Trạng thái hiện tại của Coil(31)

    // Kiểm tra nếu Coil(31) chuyển từ 1 sang 0
    if (previousCoil31State && !currentCoil31State)
    {
        // Nếu Coil(30) vừa chuyển từ 1 sang 0 thì tắt Coil(0) và Coil(1)
        Serial.println("Coil(31) chuyển từ 1 về 0, tắt Coil(0) và Coil(1)");
        mb.Coil(2, LOW); // Tắt Coil(2)
        mb.Coil(3, LOW); // Tắt Coil(3)
    }

    // Nếu Coil(31) đang ở trạng thái 1 thì chạy chương trình LogicPH
    if (currentCoil31State)
    {
        LogicPH(); // Chạy chương trình LogicPH khi mb.Coil(31) = 1
    }

    checkInput8();
    Printdosing();
    CalibSensor();
    mb.task();
    yield();

    // Cập nhật trạng thái trước đó của Coil(30) cho lần lặp tiếp theo
    previousCoil30State = currentCoil30State;
    previousCoil31State = currentCoil31State;
}

void UpdateValue()
{
    dosi_EC.setTds = mb.Hreg(0);
    dosi_EC.dosingTime_Tds = mb.Hreg(2);
    dosi_EC.waitTime_Tds = mb.Hreg(1);
    dosi_EC.ratio_A = mb.Hreg(3);
    dosi_EC.ratio_B = mb.Hreg(4);
    dosi_EC.alarm_Tds_max = mb.Hreg(9);
    dosi_EC.alarm_Tds_min = mb.Hreg(10);

    dosi_PH.setPh = (mb.Hreg(5)) / 100;
    dosi_PH.dosingTime_Ph = mb.Hreg(6);
    dosi_PH.waitTime_Ph = mb.Hreg(7);
    dosi_PH.alarm_Ph_max = mb.Hreg(11);
    dosi_PH.alarm_Ph_min = mb.Hreg(12);
    calib.EC = mb.Hreg(20);
}

void UpdateSensor()
{
    mb.Ireg(0, GetSS.Tds);
    mb.Ireg(1, GetSS.Ph);
    mb.Ireg(2, GetSS.Temp);
    mb.Ireg(3, GetSS.Ec);
}

void GetDataSensor()
{
    static long pretime = millis();
    if (millis() - pretime >= 1000)
    {
        pretime = millis();
        float newEcValue = sensor.getEcValue(); // Lấy giá trị mới từ cảm biến

        // Cập nhật giá trị Ec với giá trị trung bình di động
        GetSS.Ec = updateMovingAverage(newEcValue);
        GetSS.Tds = GetSS.Ec; // *0.5 nếu dùng tds, còn ec thì để nguyên
        GetSS.Ph = sensor.getPhValue() * 100;
        GetSS.Temp = sensor.getTemperature() * 100;
    }
}

void LogicEC()
{
    static unsigned long ecDosingStartTime = 0; // Thời gian bắt đầu bơm cho cả hai Coil
    static bool isECDosing = false;             // Trạng thái bơm chung cho cả hai Coil
    static bool isWaiting = false;              // Trạng thái chờ chung cho cả hai Coil
    static bool coilAOff = false;               // Trạng thái Coil(0) đã tắt
    static bool coilBOff = false;               // Trạng thái Coil(1) đã tắt

    // Tính thời gian bơm cho Coil(0) dựa trên ratio_A
    int Timedosi_A = (dosi_EC.dosingTime_Tds * dosi_EC.ratio_A / 100) * 1000;

    // Tính thời gian bơm cho Coil(1) dựa trên ratio_B
    int Timedosi_B = (dosi_EC.dosingTime_Tds * dosi_EC.ratio_B / 100) * 1000;

    // Thời gian chờ giữa các lần bơm cho cả hai cuộn
    int WaitTime = dosi_EC.waitTime_Tds * 1000;

    // Kiểm tra nếu GetSS.Ec lớn hơn setTds thì tắt ngay Coil(0) và Coil(1)
    if (GetSS.Ec > dosi_EC.setTds)
    {
        // Serial.println("GetSS.Ec lớn hơn setTds, tắt Coil(0) và Coil(1)");
        mb.Coil(0, LOW);
        mb.Coil(1, LOW);
        isECDosing = false; // Kết thúc bơm
        isWaiting = false;  // Không chờ nữa
        return;             // Dừng thực hiện tiếp logic
    }

    // Nếu không đang bơm và không đang chờ, bắt đầu bơm cả hai Coil
    if (!isECDosing && !isWaiting)
    {
        // Serial.println("Bắt đầu bơm cả Coil(0) và Coil(1)");
        ecDosingStartTime = millis();
        isECDosing = true;
        coilAOff = false;
        coilBOff = false;
        mb.Coil(0, HIGH); // Bật Coil(0)
        mb.Coil(1, HIGH); // Bật Coil(1)
    }

    // Kiểm tra thời gian để tắt Coil(0)
    if (isECDosing && !coilAOff && (millis() - ecDosingStartTime >= Timedosi_A))
    {
        // Serial.println("Dừng bơm Coil(0)");
        mb.Coil(0, LOW);
        coilAOff = true; // Đánh dấu Coil(0) đã tắt
    }

    // Kiểm tra thời gian để tắt Coil(1)
    if (isECDosing && !coilBOff && (millis() - ecDosingStartTime >= Timedosi_B))
    {
        // Serial.println("Dừng bơm Coil(1)");
        mb.Coil(1, LOW);
        coilBOff = true; // Đánh dấu Coil(1) đã tắt
    }

    // Nếu cả hai cuộn đã tắt, bắt đầu thời gian chờ
    if (coilAOff && coilBOff && isECDosing)
    {
        // Serial.println("Cả hai Coil đã tắt, bắt đầu thời gian chờ");
        isECDosing = false;           // Kết thúc bơm
        isWaiting = true;             // Bắt đầu chờ
        ecDosingStartTime = millis(); // Bắt đầu đếm thời gian chờ
    }

    // Sau khi đã dừng bơm, đợi hết thời gian chờ
    if (isWaiting && (millis() - ecDosingStartTime >= WaitTime))
    {
        // Serial.println("Kết thúc thời gian chờ");
        isWaiting = false; // Kết thúc chờ, sẵn sàng cho chu kỳ bơm tiếp theo
    }
}

unsigned long dosingStart_Ph = 0; // Biến lưu thời gian bắt đầu dosing
unsigned long waitStart_Ph = 0;   // Biến lưu thời gian bắt đầu chờ

// Hàm cập nhật giá trị trung bình động cho Ph, giúp lọc nhiễu
float updatePhMovingAverage(float newValue)
{
    static float phBuffer[MOVING_AVERAGE_WINDOW] = {0};
    static int phIndex = 0;
    static float phSum = 0;

    phSum -= phBuffer[phIndex];   // Trừ giá trị cũ khỏi tổng
    phBuffer[phIndex] = newValue; // Gán giá trị mới vào bộ đệm
    phSum += newValue;            // Thêm giá trị mới vào tổng
    phIndex = (phIndex + 1) % MOVING_AVERAGE_WINDOW;

    return phSum / MOVING_AVERAGE_WINDOW; // Trả về giá trị trung bình
}

void LogicPH()
{
    // Cập nhật giá trị trung bình động cho Ph
    float filteredPh = updatePhMovingAverage(GetSS.Ph);

    // Kiểm tra khoảng sai số 5% quanh setPh
    if (filteredPh < dosi_PH.setPh * 0.95)
    { // Nếu Ph nhỏ hơn setPh với sai số 5%
        if (millis() - dosingStart_Ph >= dosi_PH.dosingTime_Ph)
        {                              // Đảm bảo thời gian dosing
            mb.Coil(2, true);          // Bật Coil(2)
            mb.Coil(3, false);         // Tắt Coil(3)
            dosingStart_Ph = millis(); // Khởi tạo lại thời gian dosing
            waitStart_Ph = millis();   // Đặt lại thời gian chờ
        }
    }
    else if (filteredPh > dosi_PH.setPh * 1.05)
    { // Nếu Ph lớn hơn setPh với sai số 5%
        if (millis() - dosingStart_Ph >= dosi_PH.dosingTime_Ph)
        {                              // Đảm bảo thời gian dosing
            mb.Coil(3, true);          // Bật Coil(3)
            mb.Coil(2, false);         // Tắt Coil(2)
            dosingStart_Ph = millis(); // Khởi tạo lại thời gian dosing
            waitStart_Ph = millis();   // Đặt lại thời gian chờ
        }
    }
    else
    { // Khi Ph nằm trong khoảng sai số 5%
        if (millis() - waitStart_Ph >= dosi_PH.waitTime_Ph)
        {                      // Sau thời gian chờ
            mb.Coil(2, false); // Tắt Coil(2)
            mb.Coil(3, false); // Tắt Coil(3)
        }
    }
}

void checkInput8()
{
    static unsigned long startTime = millis(); // Thời gian bắt đầu của 2 giây
    static int highCount = 0;                  // Số lần tín hiệu ở trạng thái HIGH
    static int lowCount = 0;                   // Số lần tín hiệu ở trạng thái LOW
    const unsigned long checkInterval = 2000;  // Khoảng thời gian 2 giây

    // Đọc trạng thái hiện tại của I8
    int currentInput8State = digitalRead(I5);

    // Cập nhật bộ đếm dựa trên trạng thái tín hiệu
    if (currentInput8State == LOW)
    {
        highCount++;
    }
    else
    {
        lowCount++;
    }

    // Kiểm tra nếu đã qua 2 giây
    if (millis() - startTime >= checkInterval)
    {
        // So sánh số lần HIGH và LOW
        if (highCount < lowCount)
        {
            // Nếu HIGH nhiều hơn, coi tín hiệu là HIGH
            mb.Coil(4, LOW);   // Tắt Coil(4)
            mb.Coil(25, HIGH); // Bật Coil(25)
        }
        else
        {
            // Nếu LOW nhiều hơn, coi tín hiệu là LOW
            mb.Coil(25, LOW); // Tắt Coil(25)
        }

        // Reset lại bộ đếm và thời gian để bắt đầu chu kỳ mới
        startTime = millis();
        highCount = 0;
        lowCount = 0;
    }
}

void Printdosing()
{
    static long delePrint = millis();
    if (millis() - delePrint >= 500)
    {
        delePrint = millis();

        // In các giá trị liên quan đến TDS
        // Serial.print("TDS Set Point: ");
        // Serial.println(dosi_EC.setTds);

        // Serial.print("Dosing Time (TDS): ");
        // Serial.println(dosi_EC.dosingTime_Tds);

        // Serial.print("Wait Time (TDS): ");
        // Serial.println(dosi_EC.waitTime_Tds);

        // Serial.print("Ratio A: ");
        // Serial.println(dosi_EC.ratio_A);

        // Serial.print("Ratio B: ");
        // Serial.println(dosi_EC.ratio_B);

        // Serial.print("TDS Alarm Max: ");
        // Serial.println(dosi_EC.alarm_Tds_max);

        // Serial.print("TDS Alarm Min: ");
        // Serial.println(dosi_EC.alarm_Tds_min);

        // Serial.println("/////");
        // Serial.println("/////");
        // Serial.println(" ");
        // Serial.println(" ");

        // // In các giá trị liên quan đến pH
        // Serial.print("pH Set Point: ");
        // Serial.println(dosi_PH.setPh);

        // Serial.print("Dosing Time (pH): ");
        // Serial.println(dosi_PH.dosingTime_Ph);

        // Serial.print("Wait Time (pH): ");
        // Serial.println(dosi_PH.waitTime_Ph);

        // Serial.print("pH Alarm Max: ");
        // Serial.println(dosi_PH.alarm_Ph_max);

        // Serial.print("pH Alarm Min: ");
        // Serial.println(dosi_PH.alarm_Ph_min);

        // // In giá trị cảm biến TDS, pH, và nhiệt độ
        Serial.print("TDS                               : ");
        Serial.println(GetSS.Tds);

        // Serial.print("PH: ");
        // Serial.println(GetSS.Ph / 100);

        // Serial.print("TEMP: ");
        // Serial.print(GetSS.Temp / 100);
        // Serial.println(" *C");

        // Serial.println("//////////////////////////////////////////////////////");
        // Serial.println("//////////////////////////////////////////////////////");
        // Serial.println("//////////////////////////////////////////////////////");

        // Đọc và in trạng thái của các ngõ vào (input)
        // for (int i = 0; i < 8; i++)
        // {
        //     int inputState = digitalRead(input[4]); // Đọc trạng thái của ngõ vào
        //     Serial.print("Trạng thái của ngõ vào I");
        //     Serial.print(5); // In số ngõ vào (I1 đến I8)
        //     Serial.print(": ");
        //     Serial.println(inputState); // In trạng thái ngõ vào (0 hoặc 1)
        //     Serial.println("**********************");
        // }
    }
    // int inputState = digitalRead(input[7]); // Đọc trạng thái của ngõ vào
    // Serial.print("Trạng thái của ngõ vào I");
    // Serial.print(7); // In số ngõ vào (I1 đến I8)
    // Serial.print(": ");
    // Serial.println(inputState); // In trạng thái ngõ vào (0 hoặc 1)
    // Serial.println("**********************");
}

void CalibSensor()
{
    bool ecCalibCurrentState = mb.Coil(20);
    if (ecCalibCurrentState && !ecCalibPreviousState)
    {
        sendEcCalibValue();
    }
    ecCalibPreviousState = ecCalibCurrentState;

    bool phCalibCurrentState4 = mb.Coil(21);
    if (phCalibCurrentState4 && !phCalibPreviousState4)
    {
        sendPhCalibValue(4);
    }
    phCalibPreviousState4 = phCalibCurrentState4;

    bool phCalibCurrentState7 = mb.Coil(22);
    if (phCalibCurrentState7 && !phCalibPreviousState7)
    {
        sendPhCalibValue(7);
    }
    phCalibPreviousState7 = phCalibCurrentState7;
}
void sendEcCalibValue()
{
    String jsonRequest = "{\"ec_calib\":" + String(calib.EC) + "}";
    sensor.sendRequest(jsonRequest);
}

void sendPhCalibValue(int value)
{
    String jsonRequest = "{\"ph_calib\":" + String(value) + "}";
    sensor.sendRequest(jsonRequest);
    // Serial.print("Đã gửi chuỗi: ");
    // Serial.println(jsonRequest);
}

float updateMovingAverage(float newValue)
{
    // Kiểm tra nếu giá trị mới nhỏ hơn 90% giá trị trước
    if (newValue < ecBuffer[ecIndex] * 0.1)
    {
        newValue = ecBuffer[ecIndex]; // Đặt giá trị mới bằng giá trị trước đó
    }

    ecSum -= ecBuffer[ecIndex];   // Trừ giá trị cũ ra khỏi tổng
    ecBuffer[ecIndex] = newValue; // Thêm giá trị mới vào buffer
    ecSum += newValue;            // Cộng giá trị mới vào tổng

    ecIndex = (ecIndex + 1) % MOVING_AVERAGE_WINDOW; // Cập nhật chỉ số

    return ecSum / MOVING_AVERAGE_WINDOW; // Trả về giá trị trung bình
}

// float updatePhMovingAverage(float newPhValue)
// {
//     float currentAverage = phSum / PH_MOVING_AVERAGE_WINDOW; // Tính giá trị trung bình hiện tại

//     // Kiểm tra nếu giá trị mới nhỏ hơn 90% của giá trị trung bình hiện tại
//     if (newPhValue < 0.9 * currentAverage)
//     {
//         newPhValue = currentAverage; // Đặt giá trị mới bằng giá trị trung bình hiện tại để lọc nhiễu
//     }

//     // Cập nhật bộ lọc trung bình động
//     phSum -= phBuffer[phIndex];                         // Loại bỏ giá trị cũ khỏi tổng
//     phBuffer[phIndex] = newPhValue;                     // Thêm giá trị mới vào mảng đệm
//     phSum += newPhValue;                                // Cập nhật tổng với giá trị mới
//     phIndex = (phIndex + 1) % PH_MOVING_AVERAGE_WINDOW; // Chuyển sang vị trí tiếp theo

//     return phSum / PH_MOVING_AVERAGE_WINDOW; // Trả về giá trị trung bình động mới
// }

void reconnectWiFi()
{
    // Kiểm tra trạng thái WiFi
    if (WiFi.status() != WL_CONNECTED)
    {
        Serial.println("Mất kết nối WiFi. Đang cố gắng kết nối lại...");

        // Thực hiện kết nối lại
        WiFi.disconnect();
        WiFi.begin(ssid, password);

        // Chờ một khoảng thời gian để kết nối lại
        unsigned long startAttemptTime = millis();
        while (WiFi.status() != WL_CONNECTED && millis() - startAttemptTime < 10000)
        {
            delay(500);
            Serial.print(".");
        }

        // Kiểm tra kết quả kết nối lại
        if (WiFi.status() == WL_CONNECTED)
        {
            Serial.println("");
            Serial.println("Đã kết nối lại WiFi!");
            Serial.print("Địa chỉ IP: ");
            Serial.println(WiFi.localIP());
        }
        else
        {
            Serial.println("");
            Serial.println("Kết nối lại WiFi thất bại.");
        }
    }
}
