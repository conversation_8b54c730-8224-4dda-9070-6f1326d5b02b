#include "Arduino.h"
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include "MLBoard8CH.h"
#include "ModbusRTU.h"
#include <ModbusIP_ESP8266.h>
#include <WiFi.h> // Thê<PERSON> thư viện WiFi

#define SLAVE_ID 1

ModbusRTU *rtu = new ModbusRTU();
MLBoard8CH *board8io = new MLBoard8CH();
ModbusIP mb;

bool ecCalibPreviousState = false;
bool phCalibPreviousState4 = false;
bool phCalibPreviousState7 = false;

void setup();
void loop();
void UpdateValue();
void Printdosing();
void UpdateSensor();
void GetDataSensor();
void LogicEC();
void LogicPH();
void checkInput8();
void CalibSensor();
void sendEcCalibValue();
void sendPhCalibValue(int value);
void connectToWiFi(); // Thêm khai b<PERSON>o hàm kết nối WiFi
void reconnectWiFi();
struct DosingParameters
{
    float setTds;
    unsigned long dosingTime_Tds;
    unsigned long waitTime_Tds;
    int ratio_A;
    int ratio_B;
    float alarm_Tds_max;
    float alarm_Tds_min;
};
DosingParameters dosi_EC;

struct DosingPH
{
    float setPh;
    unsigned long dosingTime_Ph;
    unsigned long waitTime_Ph;
    float alarm_Ph_max;
    float alarm_Ph_min;
};
DosingPH dosi_PH;

struct Sensor_EC
{
    float Tds;
    float Ec;
    float Ph;
    float Temp;
};
Sensor_EC GetSS;

struct Calib_Sensor
{
    float EC;
    float Ph;
    float Temp;
};
Calib_Sensor calib;

// Cấu hình bộ lọc nhiễu cho EC
float updateMovingAverage(float newValue);
static const int MOVING_AVERAGE_WINDOW = 20;
float ecBuffer[MOVING_AVERAGE_WINDOW] = {0};
int ecIndex = 0;
float ecSum = 0;

// Cấu hình bộ lọc nhiễu cho PH
// float updatePhMovingAverage(float newPhValue);
static const int PH_MOVING_AVERAGE_WINDOW = 20; // Kích thước cửa sổ trung bình động cho PH
float phBuffer[PH_MOVING_AVERAGE_WINDOW] = {0}; // Mảng đệm cho giá trị PH
int phIndex = 0;                                // Chỉ số hiện tại trong mảng đệm
float phSum = 0;                                // Tổng các giá trị trong mảng đệm
