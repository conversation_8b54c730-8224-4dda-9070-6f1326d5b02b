#include <Arduino.h>
#include <SPI.h>
#include <Ethernet.h>
#include <EthernetServer.h>
#include <ModbusEthernet.h>
#include "RTU_EC.h"

// Đ<PERSON><PERSON> nghĩa các coil
#define COIL_LOGIC_EC 30       // Bật/tắt logic EC
#define COIL_LOGIC_PH 31       // Bật/tắt logic PH
#define COIL_PUMP_IRRIGATION 0 // Bơm tưới
#define COIL_PUMP_EC1 1        // Bơm EC 1
#define COIL_PUMP_EC2 2        // Bơm PH 2
#define COIL_PUMP_PH 3         // Bơm PH 3
#define COIL_WATER_INLET 4     // Van nước vào
#define COIL_VALVE_1 5         // Van 1
#define COIL_VALVE_2 6         // Van 2
#define COIL_VALVE_3 7         // Van 3
// Đ<PERSON><PERSON> nghĩa các input
#define IN_PRESSURE_SENSOR 1  // C<PERSON>m biến áp suất
#define IN_FLOW_SENSOR 2      // Cảm biến dòng chảy
#define IN_PUMP_SENSOR 3      // Cảm biến bồn phân 1:IN_PUMP_SENSOR
#define IN_WATER_LEVEL_HIGH 4 // Cảm biến bồn phân 2:IN_WATER_LEVEL_HIGH
#define IN_WATER_LEVEL_LOW 5  // Cảm biến mực nước dưới:IN_WATER_LEVEL_LOW
#define IN_TANK_1_SENSOR 6    // Cảm biến mực nước trên:IN_TANK_1_SENSOR
#define IN_TANK_2_SENSOR 15   // Cảm biến bơm:IN_TANK_2_SENSOR

// Ánh xạ các input với coil Modbus
#define COIL_PRESSURE_SENSOR 10  // Coil cảm biến áp suất
#define COIL_FLOW_SENSOR 11      // Coil cảm biến dòng chảy
#define COIL_PUMP_SENSOR 12      // Coil cảm biến bồn phân 1:COIL_PUMP_SENSOR
#define COIL_WATER_LEVEL_HIGH 13 // Coil cảm biến bồn phân 2:COIL_WATER_LEVEL_HIGH
#define COIL_WATER_LEVEL_LOW 14  // Coil cảm biến mực nước dưới:COIL_WATER_LEVEL_LOW
#define COIL_TANK_1_SENSOR 15    // Coil cảm biến mực nước trên:COIL_TANK_1_SENSOR
#define COIL_TANK_2_SENSOR 16    // Coil cảm biến bơm:COIL_TANK_2_SENSOR

// Đối tượng Modbus
ModbusEthernet mb;

// Cấu hình GPIO input/output
uint8_t input[8] = {1, 2, 3, 4, 5, 6, 15, 16};        // Nhập GPIO input tại đây
uint8_t output[8] = {20, 19, 21, 42, 41, 40, 39, 38}; // Nhập GPIO output tại đây

// Trạng thái thực hiện task
enum TaskState
{
  READ_SLAVE,
  WRITE_SLAVE,
  CONTROL_COILS
};
TaskState taskState = READ_SLAVE; // Trạng thái ban đầu

// Định nghĩa cấu trúc dữ liệu
struct Dosing
{
  float setTds;
  uint16_t dosingTime_Tds;
  uint16_t waitTime_Tds;
  uint16_t ratio_A;
  uint16_t ratio_B;
  uint16_t alarm_Tds_max;
  uint16_t alarm_Tds_min;

  float setPh;
  uint16_t dosingTime_Ph;
  uint16_t waitTime_Ph;
  uint16_t alarm_Ph_max;
  uint16_t alarm_Ph_min;
};
Dosing dosi_EC;
Dosing dosi_PH;

struct Calibration
{
  uint16_t EC;
};
Calibration calib;

// Thời gian giữa các trạng thái
unsigned long previousMillis = 0;
const unsigned long interval = 500;

// Cấu hình Ethernet
byte mac[] = {0xDE, 0xAD, 0xBE, 0xEF, 0xFE, 0xED};
IPAddress ip(192, 168, 0, 177);

void RTU_EC();
void UpdateValue();
void updateInputCoils();
void printValues();
void printSensorValues();
void LogicEC();
void LogicPH();
void handlePressureSensor();
void handleFlowSensor();
void handleWaterLevelHigh();

void setup()
{
  // Khởi tạo Serial và Ethernet
  Serial.begin(115200);
  // Cấu hình input và output GPIO
  for (int i = 0; i < 8; i++)
  {
    pinMode(input[i], INPUT);
    pinMode(output[i], OUTPUT);
  }
  Ethernet.begin(mac, ip);

  Serial.print("IP Address: ");
  Serial.println(Ethernet.localIP());

  // Khởi tạo Modbus
  mb.server();
  mb.addCoil(0, 0, 50); // 8 Coil (điều khiển output)
  mb.addHreg(0, 0, 50); // 8 Input Status (trạng thái input)
  mb.addIreg(0, 0, 50);

  // Khởi tạo Modbus RTU
  Serial2.begin(9600, SERIAL_8N1, 18, 17); // TX=18, RX=17
  mb_rtu.begin(&Serial2);
  mb_rtu.slave(1);
  mb_rtu.master();
}

bool previousCoil30State = false; // Lưu trạng thái trước đó của Coil(30)
bool previousCoil31State = false; // Lưu trạng thái trước đó của Coil(30)

void loop()
{
  // Cập nhật trạng thái GPIO từ Modbus Coil
  for (int i = 0; i < 8; i++)
  {
    digitalWrite(output[i], mb.Coil(i));
  }
  // Gọi hàm xử lý cảm biến IN_WATER_LEVEL_HIGH
  handleWaterLevelHigh();
  // Xử lý Modbus và RTU
  RTU_EC();
  // Cập nhật giá trị từ Modbus
  UpdateValue();
  // Cập nhật trạng thái của các input và ánh xạ vào coil Modbus
  updateInputCoils();
  // In giá trị để kiểm tra (tùy chọn)
  printValues();
  // Xử lý Logic EC
  bool currentCoil30State = mb.Coil(30); // Trạng thái hiện tại của Coil(30)

  // Kiểm tra nếu Coil(30) chuyển từ 1 sang 0
  if (previousCoil30State && !currentCoil30State)
  {
    // Nếu Coil(30) vừa chuyển từ 1 sang 0 thì tắt Coil(0) và Coil(1)
    Serial.println("Coil(30) chuyển từ 1 về 0, tắt Coil(0) và Coil(1)");
    mb.Coil(COIL_PUMP_EC1, LOW);
    mb.Coil(COIL_PUMP_EC2, LOW);
  }

  // Nếu Coil(30) đang ở trạng thái 1 thì chạy chương trình LogicEC
  if (currentCoil30State)
  {
    LogicEC(); // Chạy chương trình LogicEC khi mb.Coil(30) = 1
  }
  // Xử lý Logic PH
  bool currentCoil31State = mb.Coil(31); // Trạng thái hiện tại của Coil(31)

  // Kiểm tra nếu Coil(31) chuyển từ 1 sang 0
  if (previousCoil31State && !currentCoil31State || dosi_PH.setPh == 0)
  {
    // Nếu Coil(30) vừa chuyển từ 1 sang 0 thì tắt Coil(0) và Coil(1)
    Serial.println("Coil(31) chuyển từ 1 về 0, tắt Coil(0) và Coil(1)");
    mb.Coil(COIL_PUMP_PH, LOW); // Tắt bơm PH
  }

  // Nếu Coil(31) đang ở trạng thái 1 thì chạy chương trình LogicPH
  if (currentCoil31State)
  {
    LogicPH(); // Chạy chương trình LogicPH khi mb.Coil(31) = 1
  }

  // Xử lý logic cảm biến áp suất
  handlePressureSensor();
  // Xử lý logic cảm biến dòng chảy
  handleFlowSensor();
  // Xử lý Modbus
  mb.task();
  mb_rtu.task();
  yield();

  // Cập nhật trạng thái trước đó của Coil(30) cho lần lặp tiếp theo
  previousCoil30State = currentCoil30State;
  previousCoil31State = currentCoil31State;
}

void UpdateValue()
{
  // Cập nhật giá trị EC từ các thanh ghi
  dosi_EC.setTds = mb.Hreg(0);
  dosi_EC.dosingTime_Tds = mb.Hreg(2);
  dosi_EC.waitTime_Tds = mb.Hreg(1);
  dosi_EC.ratio_A = mb.Hreg(3);
  dosi_EC.ratio_B = mb.Hreg(4);
  dosi_EC.alarm_Tds_max = mb.Hreg(9);
  dosi_EC.alarm_Tds_min = mb.Hreg(10);

  // Cập nhật giá trị pH từ các thanh ghi
  dosi_PH.setPh = (mb.Hreg(5));
  dosi_PH.dosingTime_Ph = mb.Hreg(6);
  dosi_PH.waitTime_Ph = mb.Hreg(7);
  dosi_PH.alarm_Ph_max = mb.Hreg(11);
  dosi_PH.alarm_Ph_min = mb.Hreg(12);

  // Cập nhật giá trị hiệu chỉnh
  calib.EC = mb.Hreg(20);
}
void updateInputCoils()
{
  // Cập nhật và in trạng thái của từng coil
  bool pressureSensor = !digitalRead(IN_PRESSURE_SENSOR);
  mb.Coil(COIL_PRESSURE_SENSOR, pressureSensor);
  // Serial.print("COIL_PRESSURE_SENSOR: ");
  // Serial.println(pressureSensor);

  bool flowSensor = !digitalRead(IN_FLOW_SENSOR);
  mb.Coil(COIL_FLOW_SENSOR, flowSensor);
  // Serial.print("COIL_FLOW_SENSOR: ");
  // Serial.println(flowSensor);

  bool tank1Sensor = !digitalRead(IN_TANK_1_SENSOR);
  mb.Coil(COIL_TANK_1_SENSOR, tank1Sensor);
  // Serial.print("COIL_TANK_1_SENSOR: ");
  // Serial.println(tank1Sensor);

  bool tank2Sensor = !digitalRead(IN_TANK_2_SENSOR);
  mb.Coil(COIL_TANK_2_SENSOR, tank2Sensor);
  // Serial.print("COIL_TANK_2_SENSOR: ");
  // Serial.println(tank2Sensor);

  bool waterLevelLow = !digitalRead(IN_WATER_LEVEL_LOW);
  mb.Coil(COIL_WATER_LEVEL_LOW, waterLevelLow);
  // Serial.print("COIL_WATER_LEVEL_LOW: ");
  // Serial.println(waterLevelLow);

  bool waterLevelHigh = !digitalRead(IN_WATER_LEVEL_HIGH);
  mb.Coil(COIL_WATER_LEVEL_HIGH, waterLevelHigh);
  // Serial.print("COIL_WATER_LEVEL_HIGH: ");
  // Serial.println(waterLevelHigh);

  bool pumpSensor = digitalRead(IN_PUMP_SENSOR);
  mb.Coil(COIL_PUMP_SENSOR, pumpSensor);
  // Serial.print("COIL_PUMP_SENSOR: ");
  // Serial.println(pumpSensor);
}

void printValues()
{
  static unsigned long previousPrintMillis = 0; // Thời gian in cuối cùng
  const unsigned long printInterval = 1000;     // Chu kỳ in (1 giây)

  if (millis() - previousPrintMillis >= printInterval)
  {
    previousPrintMillis = millis(); // Cập nhật thời gian in cuối

    // In các giá trị
    // Serial.println("=== Sensor Values ===");
    Serial.print("EC Set TDS: ");
    Serial.println(dosi_EC.setTds);
    // Serial.print("EC Dosing Time: ");
    // Serial.println(dosi_EC.dosingTime_Tds);
    // Serial.print("EC Wait Time: ");
    // Serial.println(dosi_EC.waitTime_Tds);
    // Serial.print("Ratio A: ");
    // Serial.println(dosi_EC.ratio_A);
    // Serial.print("Ratio B: ");
    // Serial.println(dosi_EC.ratio_B);
    // Serial.print("EC Alarm Max: ");
    // Serial.println(dosi_EC.alarm_Tds_max);
    // Serial.print("EC Alarm Min: ");
    // Serial.println(dosi_EC.alarm_Tds_min);

    Serial.print("pH Set Point: ");
    Serial.println(dosi_PH.setPh);
    // Serial.print("pH Dosing Time: ");
    // Serial.println(dosi_PH.dosingTime_Ph);
    // Serial.print("pH Wait Time: ");
    // Serial.println(dosi_PH.waitTime_Ph);
    // Serial.print("pH Alarm Max: ");
    // Serial.println(dosi_PH.alarm_Ph_max);
    // Serial.print("pH Alarm Min: ");
    // Serial.println(dosi_PH.alarm_Ph_min);

    // Serial.print("Calibration EC: ");
    // Serial.println(mb.Hreg(HREG_CALIB_EC));
    // Serial.println("=====================");
  }
}
void LogicEC()
{
  static unsigned long ecDosingStartTime = 0;
  static bool isECDosing = false;
  static bool isWaiting = false;
  static bool coilAOff = false;
  static bool coilBOff = false;

  int Timedosi_A = (dosi_EC.dosingTime_Tds * dosi_EC.ratio_A / 100) * 1000;
  int Timedosi_B = (dosi_EC.dosingTime_Tds * dosi_EC.ratio_B / 100) * 1000;
  int WaitTime = dosi_EC.waitTime_Tds * 1000;

  if (ecValue > dosi_EC.setTds)
  {
    mb.Coil(COIL_PUMP_EC1, LOW);
    mb.Coil(COIL_PUMP_EC2, LOW);
    isECDosing = false;
    isWaiting = false;
    return;
  }

  if (!isECDosing && !isWaiting)
  {
    ecDosingStartTime = millis();
    isECDosing = true;
    coilAOff = false;
    coilBOff = false;
    mb.Coil(COIL_PUMP_EC1, HIGH);
    mb.Coil(COIL_PUMP_EC2, HIGH);
  }

  if (isECDosing && !coilAOff && (millis() - ecDosingStartTime >= Timedosi_A))
  {
    mb.Coil(COIL_PUMP_EC1, LOW);
    coilAOff = true;
  }

  if (isECDosing && !coilBOff && (millis() - ecDosingStartTime >= Timedosi_B))
  {
    mb.Coil(COIL_PUMP_EC2, LOW);
    coilBOff = true;
  }

  if (coilAOff && coilBOff && isECDosing)
  {
    isECDosing = false;
    isWaiting = true;
    ecDosingStartTime = millis();
  }

  if (isWaiting && (millis() - ecDosingStartTime >= WaitTime))
  {
    isWaiting = false;
  }
}

void LogicPH()
{
  // Biến cục bộ
  unsigned long dosingStart_Ph = 0;
  unsigned long waitStart_Ph = 0;

  // Kiểm tra nếu pH lớn hơn setPh với sai số 2%
  if (phValue > dosi_PH.setPh * 1.02)
  { // Nếu Ph lớn hơn setPh với sai số 5%
    if (millis() - dosingStart_Ph >= dosi_PH.dosingTime_Ph)
    {                              // Đảm bảo thời gian dosing
      mb.Coil(COIL_PUMP_PH, true); // Bật bơm PH
      dosingStart_Ph = millis();   // Khởi tạo lại thời gian dosing
      waitStart_Ph = millis();     // Đặt lại thời gian chờ
    }
  }
  else
  { // Khi Ph nằm trong khoảng sai số 5% (Ph <= setPh * 1.05)
    if (millis() - waitStart_Ph >= dosi_PH.waitTime_Ph)
    {                               // Sau thời gian chờ
      mb.Coil(COIL_PUMP_PH, false); // Tắt bơm PH
    }
  }
}

void handlePressureSensor()
{
  static unsigned long pressureSensorStartTime = 0;
  static bool isPressureActive = false;

  // Kiểm tra trạng thái của cảm biến áp suất
  if (!digitalRead(IN_PRESSURE_SENSOR) == HIGH)
  {
    if (!isPressureActive)
    {
      // Bắt đầu đếm thời gian nếu tín hiệu áp suất vừa được kích hoạt
      pressureSensorStartTime = millis();
      isPressureActive = true;
    }
    else if (millis() - pressureSensorStartTime >= 3000)
    {
      // Nếu tín hiệu áp suất được giữ trong hơn 5 giây, tắt bơm tưới
      mb.Coil(COIL_PUMP_IRRIGATION, LOW);
      isPressureActive = false;
    }
  }
  else
  {
    // Reset khi tín hiệu áp suất không được kích hoạt
    isPressureActive = false;
  }
}

void handleFlowSensor()
{
  static unsigned long startTime = 0; // Biến lưu thời gian bắt đầu
  static bool timerStarted = false;   // Trạng thái xem có đang đếm thời gian không

  // Nếu IN_FLOW_SENSOR ở mức LOW và COIL_PUMP_IRRIGATION đang bật
  if (!digitalRead(IN_FLOW_SENSOR) == LOW && mb.Coil(COIL_PUMP_IRRIGATION))
  {
    if (!timerStarted)
    {
      startTime = millis(); // Lưu thời điểm bắt đầu
      timerStarted = true;  // Bắt đầu đếm thời gian
      Serial.println("111");
    }

    // Kiểm tra nếu đã qua 5 giây
    if (millis() - startTime >= 5000)
    {
      Serial.println("222");
      mb.Coil(COIL_PUMP_IRRIGATION, 0); // Tắt COIL_PUMP_IRRIGATION
      timerStarted = false;             // Reset trạng thái
    }
  }
  else
  {
    timerStarted = false; // Reset trạng thái nếu điều kiện không còn đúng
                          // Serial.println("333");
  }
}

void handleWaterLevelHigh()
{
  // Kiểm tra trạng thái của cảm biến IN_WATER_LEVEL_HIGH
  if (!digitalRead(IN_WATER_LEVEL_HIGH) == HIGH)
  {
    // Tắt COIL_WATER_INLET
    mb.Coil(COIL_WATER_INLET, LOW);

    // Tắt COIL_PUMP_WATER_IN (giả định là coil 40)
    mb.Coil(40, LOW);
  }
}

void RTU_EC()
{
  unsigned long currentMillis = millis();
  if (currentMillis - previousMillis >= interval)
  {
    previousMillis = currentMillis;

    // Xử lý từng task
    switch (taskState)
    {
    case READ_SLAVE:
      taskReadSlave();
      printSensorValues(); // In giá trị cảm biến
      taskState = WRITE_SLAVE;
      break;

    case WRITE_SLAVE:
      taskWriteSlave();
      taskState = CONTROL_COILS;
      break;

    case CONTROL_COILS:
      taskControlCoils();
      taskState = READ_SLAVE;
      break;

    default:
      taskState = READ_SLAVE;
      break;
    }
  }
}
void printSensorValues()
{
  Serial.print("EC: ");
  Serial.print(ecValue);
  Serial.print(", Temp: ");
  Serial.print(tempValue);
  Serial.print(", pH: ");
  Serial.println(phValue);
}