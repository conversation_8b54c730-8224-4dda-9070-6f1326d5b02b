#include "RTU_EC.h"
#include <ModbusEthernet.h>

// Đ<PERSON><PERSON> tượng ModbusEthernet
extern ModbusEthernet mb;

// Đ<PERSON>i tượng ModbusRTU
ModbusRTU mb_rtu;
// Biến toàn cục
float ecValue;
float tempValue;
float phValue;
uint16_t data[10]; // Mảng chứa dữ liệu đọc từ Modbus RTU

bool RTU_read()
{
    // Đọc 10 thanh ghi từ địa chỉ 0 của slave có ID=1
    if (mb_rtu.readHreg(1, 0, data, 10)) // Slave ID = 1, Start Address = 0, Length = 10
    {
        // Chuyển đổi các giá trị từ uint16_t thành float
        ecValue = data[0];   // Thanh ghi 0,
        tempValue = data[2]; // Thanh ghi 1,
        phValue = data[1];   // Thanh ghi 2,

        return true; // Đ<PERSON><PERSON> thành công
    }
    else
    {
        Serial.println("Lỗi đọc RTU Hreg!");
        return false; // Đ<PERSON><PERSON> thất bại
    }
}

void RTU_write(uint16_t address, uint16_t value)
{
    if (!mb_rtu.writeHreg(1, address, value))
    {
        Serial.println("Lỗi ghi RTU Hreg!");
    }
}

void RTU_controlCoils(uint16_t startCoil, uint8_t numCoils, bool *coilStates)
{
    if (!mb_rtu.writeCoil(1, startCoil, coilStates, numCoils))
    {
        Serial.println("Lỗi ghi RTU Coil!");
    }
}

void taskReadSlave()
{
    if (RTU_read())
    {
        // Thành công, cập nhật giá trị vào Modbus TCP
        mb.Ireg(3, ecValue);//3
        mb.Ireg(2, tempValue);//2
        mb.Ireg(1, phValue);//1
    }
}

void taskWriteSlave()
{
    RTU_write(HREG_CALIB_EC, mb.Hreg(HREG_CALIB_EC));
    mb_rtu.Hreg(20, mb.Hreg(HREG_CALIB_EC));
}

void taskControlCoils()
{
    bool coilStates[3] = {
        mb.Coil(COIL_CALIB_EC),
        mb.Coil(COIL_PH4),
        mb.Coil(COIL_PH7)};
    RTU_controlCoils(COIL_CALIB_EC, 3, coilStates);
    mb_rtu.Coil(COIL_CALIB_EC, mb.Coil(COIL_CALIB_EC));
}
