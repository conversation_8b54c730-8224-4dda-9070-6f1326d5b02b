#ifndef RTU_EC_H
#define RTU_EC_H

#include <ModbusRTU.h>

// Index cho các giá trị cảm biến
#define EC_INDEX 0
#define TEMP_INDEX 1
#define PH_INDEX 2

// Thanh ghi Modbus RTU
#define HREG_EC 0
#define HREG_TEMP 1
#define HREG_PH 2
#define HREG_CALIB_EC 20

#define COIL_CALIB_EC 20
#define COIL_PH4 21
#define COIL_PH7 22

// Biến toàn cục lưu giá trị cảm biến
extern float ecValue;     // Giá trị EC
extern float tempValue;   // Giá trị nhiệt độ
extern float phValue;     // Giá trị pH

// Khai báo đối tượng Modbus
extern ModbusRTU mb_rtu;

// Khai báo hàm
bool RTU_read();
void RTU_write(uint16_t address, uint16_t value);
void RTU_controlCoils(uint16_t startCoil, uint8_t numCoils, bool *coilStates);

void taskReadSlave();
void taskWriteSlave();
void taskControlCoils();

#endif
