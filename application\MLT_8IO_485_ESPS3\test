void handleFlowSensor() {
    static unsigned long startTime = 0; // Biến lưu thời gian bắt đầu
    static bool timerStarted = false;  // Trạng thái xem có đang đếm thời gian không

    // Nếu IN_FLOW_SENSOR ở mức LOW và COIL_PUMP_IRRIGATION đang bật
    if (!digitalRead(IN_FLOW_SENSOR) == LOW && mb.Coil(COIL_PUMP_IRRIGATION)) {
        if (!timerStarted) {
            startTime = millis();   // Lưu thời điểm bắt đầu
            timerStarted = true;   // Bắt đầu đếm thời gian
                  Serial.println("111");

        }

        // Kiểm tra nếu đã qua 5 giây
        if (millis() - startTime >= 5000) {
                            Serial.println("222");
            mb.Coil(COIL_PUMP_IRRIGATION, 0); // Tắt COIL_PUMP_IRRIGATION
            timerStarted = false;            // Reset trạng thái

        }
    } else {
        timerStarted = false; // Reset trạng thái nếu điều kiện không còn đúng
          // Serial.println("333");
}
}