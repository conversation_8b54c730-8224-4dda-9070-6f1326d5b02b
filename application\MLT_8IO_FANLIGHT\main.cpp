#include "Arduino.h"
#include <ModbusRTU.h>
#include "MLBoard8CH.h"

MLBoard8CH *board = new MLBoard8CH();

ModbusRTU mb;
bool previousState[3] = {false, false, false};

void setup()
{
    Serial.begin(9600);
    Serial2.begin(9600);
    board->begin();
    mb.begin(&Serial2);
    mb.slave(2);
    mb.addCoil(0, 0, 10);
}

void loop()
{
    // Read inputs and update Ists registers
    for (int i = 0; i < 8; i++)
    {
        mb.Ists(i, digitalRead(input[i]));
    }

    // Temporarily store coil states
    bool coil0 = mb.Coil(0);
    bool coil1 = mb.Coil(1);
    bool coil2 = mb.Coil(2);

    // Check and set output states according to the specified logic
    if (coil0 && !previousState[0])
    {
        Serial.println("low");
        mb.Coil(1, LOW); // Update Modbus coil for output[1]
        mb.Coil(2, LOW); // Update Modbus coil for output[2]
        previousState[0] = true;
        previousState[1] = false;
        previousState[2] = false;
    }
    else if (coil1 && !previousState[1])
    {
        Serial.println("med");
        mb.Coil(0, LOW); // Update Modbus coil for output[0]
        mb.Coil(2, LOW); // Update Modbus coil for output[2]
        previousState[0] = false;
        previousState[1] = true;
        previousState[2] = false;
    }
    else if (coil2 && !previousState[2])
    {
        Serial.println("high");
        mb.Coil(0, LOW); // Update Modbus coil for output[0]
        mb.Coil(1, LOW); // Update Modbus coil for output[1]
        previousState[0] = false;
        previousState[1] = false;
        previousState[2] = true;
    }

    // Update the physical outputs after processing all the logic
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], mb.Coil(i));
    }

    mb.task();
    yield();
}

// #include "Arduino.h"
// #include <ModbusRTU.h>
// #include "MLBoard8CH.h"

// MLBoard8CH *board = new MLBoard8CH();
// ModbusRTU mb;

// bool previousCoilState[10] = {false, false, false, false, false, false, false, false, false, false};

// void setup()
// {
//     Serial.begin(9600);
//     Serial2.begin(9600);
//     board->begin();
//     mb.begin(&Serial2);
//     mb.slave(2);
//     mb.addIsts(0, 0, 8);
//     mb.addCoil(0, 0, 10);
// }

// void loop()
// {
//     for (int i = 0; i < 10; i++)
//     {
//         bool currentCoilState = mb.Coil(i);
//         if (currentCoilState != previousCoilState[i])
//         {
//             Serial.print("Coil ");
//             Serial.print(i);
//             Serial.print(" changed to ");
//             Serial.println(currentCoilState ? "HIGH" : "LOW");
//             previousCoilState[i] = currentCoilState;
//         }
//         digitalWrite(output[i], currentCoilState);
//     }

//     mb.task();
//     yield();
//     // delay(100);
// }
