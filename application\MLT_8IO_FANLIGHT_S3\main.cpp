
#include <SPI.h>
#include <Ethernet.h>
#include <EthernetServer.h>
#include <ModbusEthernet.h>
#include <ModbusRTU.h>

ModbusRTU mb_rtu;
uint8_t input[8] = {1, 2, 3, 4, 5, 6, 15, 16};        // nhập gpio input vào đây
uint8_t output[8] = {20, 19, 21, 42, 41, 40, 39, 38}; // nhập gpio output vào đây
bool previousState[3] = {false, false, false};

void setup()
{
    Serial.begin(115200);
    Serial2.begin(9600, SERIAL_8N1, 18, 17);
    for (int i = 0; i < 8; i++)
    {
        pinMode(input[i], INPUT);
        pinMode(output[i], OUTPUT);
    }
    mb_rtu.addCoil(0, 0, 10);
    mb_rtu.begin(&Serial2);
    mb_rtu.slave(1);
}

void loop()
{
    for (int i = 0; i < 8; i++)
    {
        mb_rtu.Ists(i, digitalRead(input[i]));
    }
    // Temporarily store coil states
    bool coil0 = mb_rtu.Coil(0);
    bool coil1 = mb_rtu.Coil(1);
    bool coil2 = mb_rtu.Coil(2);

    // Check and set output states according to the specified logic
    if (coil0 && !previousState[0])
    {
        Serial.println("low");
        mb_rtu.Coil(1, LOW); // Update Modbus coil for output[1]
        mb_rtu.Coil(2, LOW); // Update Modbus coil for output[2]
        previousState[0] = true;
        previousState[1] = false;
        previousState[2] = false;
    }
    else if (coil1 && !previousState[1])
    {
        Serial.println("med");
        mb_rtu.Coil(0, LOW); // Update Modbus coil for output[0]
        mb_rtu.Coil(2, LOW); // Update Modbus coil for output[2]
        previousState[0] = false;
        previousState[1] = true;
        previousState[2] = false;
    }
    else if (coil2 && !previousState[2])
    {
        Serial.println("high");
        mb_rtu.Coil(0, LOW); // Update Modbus coil for output[0]
        mb_rtu.Coil(1, LOW); // Update Modbus coil for output[1]
        previousState[0] = false;
        previousState[1] = false;
        previousState[2] = true;
    }

    // Update the physical outputs after processing all the logic
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], mb_rtu.Coil(i));
    }

    mb_rtu.task();
    yield();
}
