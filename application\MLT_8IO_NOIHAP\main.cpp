#include "Arduino.h"
#include <ModbusRTU.h>
#include "MLBoard8CH.h"

// Khởi tạo đối tượng board và Modbus
MLBoard8CH *board = new MLBoard8CH();
ModbusRTU mb;

// Biến để kiểm soát thời gian in log
unsigned long lastPrintTime = 0;
const unsigned long printInterval = 1000; // 1 giây

void setup()
{
    Serial.begin(9600);
    Serial2.begin(9600);

    board->begin();     // Thiết lập chân input/output
    mb.begin(&Serial2); // Giao tiếp Modbus qua Serial2 (RS485)
    mb.slave(2);        // Đặt thiết bị này làm Slave với ID = 2

    // Khai báo vùng dữ liệu Modbus
    mb.addCoil(0, 0, 15); // Coil[0-14]
}

void loop()
{
    unsigned long currentMillis = millis();

    // Đ<PERSON><PERSON> trạng thái 8 input từ board và ghi lên Coil
    for (int i = 0; i < 8; i++)
    {
        bool state = !digitalRead(input[i]);
        mb.Coil(10 + i, state); // Mapping Coil[10-17] cho input[0-7]
    }

    // Logic ví dụ: nếu input1 HIGH → tắt Coil0, bật Coil2
    if (mb.Coil(11)) // Coil11 lưu trạng thái input[1]
    {
        mb.Coil(0, false); // Tắt Coil0
        mb.Coil(2, true);  // Bật Coil2
    }

    // Update relay từ Coil
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], mb.Coil(i));
    }

    // In log mỗi 1 giây
    if (currentMillis - lastPrintTime >= printInterval)
    {
        lastPrintTime = currentMillis;

        Serial.print("Input states: ");
        for (int i = 0; i < 8; i++)
        {
            Serial.print("I");
            Serial.print(i);
            Serial.print(":");
            Serial.print(mb.Coil(10 + i));
            Serial.print(" ");
        }
        Serial.println();
    }

    mb.task(); // Xử lý Modbus
    yield();   // Cho phép các tiến trình khác hoạt động
}
