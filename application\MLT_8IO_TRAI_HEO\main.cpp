#include <Arduino.h>
#include <ModbusRTU.h>
#include <Wire.h>
#include <EEPROM.h>
#include "MLBoard8CH.h"

#define EEPROM_SIZE 32
#define EEPROM_ADDR_SETTEMP 0

MLBoard8CH board;
ModbusRTU mb_slave_rtu;
ModbusRTU mb_rtu;

float setTemp = 30.0;
float currentTemp = 0.0;
bool isCooling = false;
bool isDaytime = true;
unsigned long tempBelowStart = 0;

uint8_t masterState = 0;
uint32_t lastMasterPoll = 0;
uint16_t regTempRaw = 0;

unsigned long lastPhaseChange = 0;
const unsigned long PHASE_DURATION = 12UL * 60UL * 60UL * 1000UL;  // 12 giờ

void checkDayNightPhase() {
    if (millis() - lastPhaseChange > PHASE_DURATION) {
        isDaytime = !isDaytime;
        lastPhaseChange = millis();
        Serial.println(isDaytime ? "Switch to DAY" : "Switch to NIGHT");
    }
}

void setup() {
    Serial.begin(115200);
    Serial2.begin(9600);
    board.begin();

    EEPROM.begin(EEPROM_SIZE);
    float tempFromEEPROM;
    EEPROM.get(EEPROM_ADDR_SETTEMP, tempFromEEPROM);
    if (!isnan(tempFromEEPROM) && tempFromEEPROM > 0 && tempFromEEPROM < 100)
        setTemp = tempFromEEPROM;

    mb_slave_rtu.begin(&Serial2);
    mb_slave_rtu.slave(2);
    mb_slave_rtu.addCoil(0, false, 10);
    mb_slave_rtu.addIsts(0, false, 8);
    mb_slave_rtu.addHreg(10, (uint16_t)(setTemp * 10));

    mb_rtu.begin(&Serial2);
}

void updateQuatLogic() {
    checkDayNightPhase();

    switch (masterState) {
        case 0:
            if (millis() - lastMasterPoll > 1000) {
                if (mb_rtu.readHreg(1, 0, &regTempRaw, 1)) {
                    masterState = 1;
                }
                lastMasterPoll = millis();
            }
            break;
        case 1:
            currentTemp = regTempRaw / 10.0;
            masterState = 0;
            break;
    }

    uint16_t rawSV = mb_slave_rtu.Hreg(10);
    float newSV = rawSV / 10.0;
    if (abs(newSV - setTemp) > 0.1) {
        setTemp = newSV;
        EEPROM.put(EEPROM_ADDR_SETTEMP, setTemp);
        EEPROM.commit();
        Serial.printf("Saved SV: %.1f to EEPROM\n", setTemp);
    }

    if (currentTemp > setTemp) {
        for (int i = 0; i < 4; i++) digitalWrite(output[i], HIGH);
        tempBelowStart = 0;
        isCooling = true;
    } else {
        if (tempBelowStart == 0) {
            tempBelowStart = millis();
        } else if (millis() - tempBelowStart > 300000) {
            isCooling = false;
        }

        if (!isCooling) {
            if (isDaytime) {
                digitalWrite(output[0], HIGH);
                digitalWrite(output[1], HIGH);
                digitalWrite(output[2], LOW);
                digitalWrite(output[3], LOW);
            } else {
                digitalWrite(output[0], LOW);
                digitalWrite(output[1], LOW);
                digitalWrite(output[2], HIGH);
                digitalWrite(output[3], HIGH);
            }
        }
    }

    for (int i = 0; i < 4; i++) {
        mb_slave_rtu.Coil(i, digitalRead(output[i]));
    }
}

void loop() {
    for (int i = 0; i < 8; i++) {
        bool val = digitalRead(input[i]);
        mb_slave_rtu.Ists(i, val);
    }

    updateQuatLogic();
    mb_rtu.task();
    mb_slave_rtu.task();
    delay(100);
}
