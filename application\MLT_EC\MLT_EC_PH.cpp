#include "MLT_EC_PH.h"
#include <ArduinoJson.h>

// Constructor
MltEcPhSensor::MltEcPhSensor() {
    sensor_uart = new HardwareSerial(1);
    ph_value = 0.0;
    ec_value = 0.0;
    temperature = 0.0;
    ec_k = 0.0;
    dataAvailable = false;
    lastRequestTime = 0;
}

void MltEcPhSensor::initialize() {
    sensor_uart->begin(9600, SERIAL_8N1, SENSOR_RX, SENSOR_TX);
    sensor_uart->setTimeout(50);
}

void MltEcPhSensor::sendRequest(const String &request) {
    sensor_uart->println(request);
}

void MltEcPhSensor::handleUartEvent() {
    if (sensor_uart->available()) {
        String jsonString = sensor_uart->readString();
        parseJson(jsonString);
    }
}

void MltEcPhSensor::parseJson(const String &jsonString) {
    StaticJsonDocument<1024> doc;
    DeserializationError error = deserializeJson(doc, jsonString);

    if (error) {
        return;
    }

    ec_value = doc["sensors"]["ec_sensor"];
    ph_value = doc["sensors"]["ph_sensor"];
    temperature = doc["sensors"]["temp_sensor"];
    ec_k = doc["sensors"]["ec_k"];
    dataAvailable = true;
}

void MltEcPhSensor::loop() {
    unsigned long currentTime = millis();
    if (currentTime - lastRequestTime >= 1000) {
        lastRequestTime = currentTime;
        sendRequest("{\"get_sensors\":1}");
    }
    handleUartEvent();
}

float MltEcPhSensor::getPhValue() {
    return ph_value;
}

float MltEcPhSensor::getEcValue() {
    return ec_value;
}

float MltEcPhSensor::getTemperature() {
    return temperature;
}

float MltEcPhSensor::getEcK() {
    return ec_k;
}
