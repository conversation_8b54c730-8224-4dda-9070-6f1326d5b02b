#ifndef MLT_ECPHSENSOR_H
#define MLT_ECPHSENSOR_H

#include "Arduino.h"
#include "HardwareSerial.h"

#define SENSOR_TX 33
#define SENSOR_RX 32

#define DEBUG_SENSOR

class MltEcPhSensor {
public:
    MltEcPhSensor();

    void initialize();
    void sendRequest(const String &request);
    void handleUartEvent();
    void loop();

    float getPhValue();
    float getEcValue();
    float getTemperature();
    float getEcK(); // Thêm getter cho ec_k

private:
    HardwareSerial* sensor_uart;
    float ph_value;
    float ec_value;
    float temperature;
    float ec_k;   // Thêm biến ec_k
    bool dataAvailable;
    unsigned long lastRequestTime;

    void parseJson(const String &jsonString);
};

#endif // MLT_ECPHSENSOR_H
