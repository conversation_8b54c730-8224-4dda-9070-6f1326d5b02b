#include "Arduino.h"
#include "main.h"
#include "MLT_EC_PH.h"
#include <WiFi.h> // Th<PERSON> viện WiFi

MltEcPhSensor sensor;

void setup()
{
  Serial.begin(9600);
  Serial2.begin(9600, SERIAL_8N1, 25, 26);

  sensor.initialize();
  rtu->begin(&Serial2);
  rtu->slave(SLAVE_ID);
  rtu->addHreg(0, 0, 50);
  rtu->addIreg(0, 0, 50);
  rtu->addCoil(0, 0, 50);
}

void loop()
{
  sensor.loop();
  UpdateValue();
  GetDataSensor();
  UpdateSensor();
  Printdosing();
  CalibSensor();
  rtu->task();
  yield();
}

void UpdateValue()
{
  calib.EC = rtu->Hreg(20);
}

void UpdateSensor()
{
  rtu->Hreg(0, GetSS.Ec);
  rtu->Hreg(1, GetSS.Ph);
  rtu->Hreg(2, GetSS.Temp);
  rtu->Hreg(3, GetSS.Tds);
  rtu->Hreg(4, sensor.getEcK()); // Ghi ec_k vào thanh ghi 4
}

void GetDataSensor()
{
  static long pretime = millis();
  if (millis() - pretime >= 1000)
  {
    pretime = millis();
    float newEcValue = sensor.getEcValue();
    GetSS.Ec = updateMovingAverage(newEcValue);

    GetSS.Tds = GetSS.Ec;
    GetSS.Ph = sensor.getPhValue() * 100;
    GetSS.Temp = sensor.getTemperature() * 100;
  }
}

void Printdosing()
{
  static long delePrint = millis();
  if (millis() - delePrint >= 5000)
  {
    delePrint = millis();
    Serial.print("TDS: ");
    Serial.println(GetSS.Tds);
    Serial.print("PH: ");
    Serial.println(GetSS.Ph / 100.0);
    Serial.print("TEMP: ");
    Serial.print(GetSS.Temp / 100.0);
    Serial.println(" *C");
    Serial.print("Calib EC: ");
    Serial.println(calib.EC);
    Serial.print("EC_K: ");
    Serial.println(sensor.getEcK());
  }
}

void CalibSensor()
{
  bool ecCalibCurrentState = rtu->Coil(20);
  if (ecCalibCurrentState && !ecCalibPreviousState)
  {
    Serial.println("Tiến hành calib EC");
    sendEcCalibValue();
  }
  ecCalibPreviousState = ecCalibCurrentState;

  bool phCalibCurrentState4 = rtu->Coil(21);
  if (phCalibCurrentState4 && !phCalibPreviousState4)
  {
    sendPhCalibValue(4);
  }
  phCalibPreviousState4 = phCalibCurrentState4;

  bool phCalibCurrentState7 = rtu->Coil(22);
  if (phCalibCurrentState7 && !phCalibPreviousState7)
  {
    sendPhCalibValue(7);
  }
  phCalibPreviousState7 = phCalibCurrentState7;
}

void sendEcCalibValue()
{
  String jsonRequest = "{\"ec_calib\":" + String(calib.EC) + "}";
  sensor.sendRequest(jsonRequest);
  Serial.print("Đang calib EC với: ");
  Serial.println(jsonRequest);
}

void sendPhCalibValue(int value)
{
  String jsonRequest = "{\"ph_calib\":" + String(value) + "}";
  sensor.sendRequest(jsonRequest);
}

float updateMovingAverage(float newValue)
{
  if (newValue < 10)
  {
    for (int i = 0; i < MOVING_AVERAGE_WINDOW; i++)
      ecBuffer[i] = 0;
    ecSum = 0;
    return 0;
  }

  ecSum -= ecBuffer[ecIndex];
  ecBuffer[ecIndex] = newValue;
  ecSum += newValue;
  ecIndex = (ecIndex + 1) % MOVING_AVERAGE_WINDOW;
  return ecSum / MOVING_AVERAGE_WINDOW;
}

float updatePhMovingAverage(float newPhValue)
{
  float currentAverage = phSum / PH_MOVING_AVERAGE_WINDOW;

  if (newPhValue < 0.5 * currentAverage)
    newPhValue = currentAverage;

  phSum -= phBuffer[phIndex];
  phBuffer[phIndex] = newPhValue;
  phSum += newPhValue;
  phIndex = (phIndex + 1) % PH_MOVING_AVERAGE_WINDOW;

  return phSum / PH_MOVING_AVERAGE_WINDOW;
}
