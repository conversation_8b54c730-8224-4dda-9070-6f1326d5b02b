#include "Arduino.h"
#include <ArduinoJson.h>
#include <SPIFFS.h>
#include "ModbusRTU.h"
#include <ModbusIP_ESP8266.h>

#define SLAVE_ID 3

ModbusRTU *rtu = new ModbusRTU();

bool ecCalibPreviousState = false;
bool phCalibPreviousState4 = false;
bool phCalibPreviousState7 = false;

void setup();
void loop();
void UpdateValue();
void Printdosing();
void UpdateSensor();
void GetDataSensor();
void CalibSensor();
void sendEcCalibValue();
void sendPhCalibValue(int value);

struct Sensor_EC
{
    float Tds;
    float Ec;
    float Ph;
    float Temp;
};
Sensor_EC GetSS;

struct Calib_Sensor
{
    float EC;
    float Ph;
    float Temp;
};
Calib_Sensor calib;

float updateMovingAverage(float newValue);
static const int MOVING_AVERAGE_WINDOW = 20;
float ecBuffer[MOVING_AVERAGE_WINDOW] = {0};
int ecIndex = 0;
float ecSum = 0;

float updatePhMovingAverage(float newPhValue);
static const int PH_MOVING_AVERAGE_WINDOW = 20;
float phBuffer[PH_MOVING_AVERAGE_WINDOW] = {0};
int phIndex = 0;
float phSum = 0;
