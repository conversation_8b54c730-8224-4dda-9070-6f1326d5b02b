#include <SPI.h>
#include <Ethernet.h>
#include <EthernetServer.h>
#include <ModbusEthernet.h>
#include <ModbusRTU.h>

byte mac[] = {0xDE, 0xAD, 0xBE, 0xEF, 0xFE, 0xED};
IPAddress ip(192, 168, 1, 177);

ModbusEthernet mb_tcp;
ModbusRTU mb;

uint8_t output[8] = {20, 19, 21, 42, 41, 40, 39, 38}; // GPIO output
bool coilState[8] = {0};                              // Trạng thái các coil

// L<PERSON>u dữ liệu từ các slave
uint16_t dataSlave[10][3];         // Lưu giá trị từ mỗi slave
float temp[10], humi[10], co2[10]; // Giá trị cảm biến
int currentSlave = 1;              // Slave hiện tại
int state = 0;                     // State machine

// Biến lưu giá trị cụ thể để ghi
uint16_t tempValue, humiValue, co2Value;
int writeIndex = 0; // <PERSON><PERSON>a chỉ ghi trong Slave 11
int writeStep = 0;  // <PERSON>õ<PERSON> bư<PERSON> ghi giá trị
void writeHregToSlave11(int reg, uint16_t value);

void setup()
{
  Serial.begin(115200);
  Serial2.begin(9600, SERIAL_8N1, 18, 17); // TX=18, RX=17

  // Cấu hình input và output GPIO
  for (int i = 0; i < 8; i++)
  {
    pinMode(output[i], OUTPUT);
  }

  Ethernet.begin(mac, ip);

  Serial.print("IP Address: ");
  Serial.println(Ethernet.localIP());

  mb_tcp.server(); // Act as Modbus TCP server
  mb_tcp.addHreg(0, 0, 10);
  mb_tcp.addCoil(0, 0, 10);

  mb.begin(&Serial2);
  mb.master();
}

void loop()
{
  switch (state)
  {
  case 0: // Đọc dữ liệu từ slave
    if (!mb.slave())
    {
      // Đọc Hreg từ slave hiện tại
      if (mb.readHreg(currentSlave, 0, dataSlave[currentSlave - 1], 3))
      {
        mb.task(); // Chạy task Modbus

        // Gán giá trị đọc được vào biến
        tempValue = dataSlave[currentSlave - 1][0];
        humiValue = dataSlave[currentSlave - 1][1];
        co2Value = dataSlave[currentSlave - 1][2];

        // Debug thông tin đọc được
        // Serial.println("Read from Slave " + String(currentSlave) +
        //  " - Temp: " + String(tempValue) +
        //  ", Humi: " + String(humiValue) +
        //  ", CO2: " + String(co2Value));

        // Chuyển sang trạng thái ghi
        state = 1;
        writeIndex = (currentSlave - 1) * 3; // Tính địa chỉ ghi trong Slave 11
        writeStep = 0;                       // Reset bước ghi
      }
      else
      {
        // Nếu đọc thất bại, in lỗi
        // Serial.println("Failed to read from Slave " + String(currentSlave));
      }

      // Chuyển đến slave tiếp theo
      currentSlave++;
      if (currentSlave > 5)
        currentSlave = 1; // Quay lại slave đầu tiên nếu hết
    }
    break;

  case 1: // Ghi từng giá trị vào slave 11 và truyền qua TCP
    if (!mb.slave())
    {
      switch (writeStep)
      {
      case 0: // Ghi giá trị nhiệt độ
        writeHregToSlave11(writeIndex, tempValue);
        // Ghi giá trị nhiệt độ vào Modbus TCP (holding register)
        mb_tcp.Hreg(writeIndex, tempValue);
        writeStep++;
        break;
      case 1: // Ghi giá trị độ ẩm
        writeHregToSlave11(writeIndex + 1, humiValue);
        // Ghi giá trị độ ẩm vào Modbus TCP (holding register)
        mb_tcp.Hreg(writeIndex + 1, humiValue);
        writeStep++;
        break;
      case 2: // Ghi giá trị CO2
        writeHregToSlave11(writeIndex + 2, co2Value);
        // Ghi giá trị CO2 vào Modbus TCP (holding register)
        mb_tcp.Hreg(writeIndex + 2, co2Value);
        writeStep++;
        break;
      default: // Hoàn tất ghi, chuyển sang đọc coil từ slave 11
        state = 2;
        break;
      }
    }
    break;

  case 2: // Đọc coil từ slave 11 để bật/tắt output
    if (!mb.slave())
    {
      // Đọc coil từ RTU (slave 11)
      if (mb.readCoil(11, 0, coilState, 8)) // Đọc 8 coil từ địa chỉ 0 qua RTU
      {
        mb.task();
        for (int i = 0; i < 8; i++)
        {
          // Nếu RTU gửi tín hiệu, ghi đè trạng thái của GPIO
          if (coilState[i])
          {
            digitalWrite(output[i], HIGH); // Bật GPIO từ RTU
          }
          else
          {
            digitalWrite(output[i], LOW); // Tắt GPIO từ RTU
          }
        }

        // Debug giá trị coil từ RTU
        // Serial.print("Slave 11 Coils (RTU): ");
        for (int i = 0; i < 8; i++)
        {
          // Serial.print(coilState[i]);
          // Serial.print(" ");
        }
        // Serial.println();
      }
      else
      {
        // Serial.println("Failed to read coils from Slave 11 (RTU)");
      }
    }

    // Đọc từng coil từ Modbus TCP
    for (int i = 0; i < 8; i++)
    {
      // Nếu TCP ghi giá trị coil, cập nhật GPIO nhưng không ghi đè RTU
      if (mb_tcp.Coil(i))
      {
        if (!coilState[i]) // Chỉ ghi đè nếu RTU không bật coil
        {
          digitalWrite(output[i], HIGH); // Bật GPIO từ TCP
        }
      }
      else
      {
        if (!coilState[i]) // Chỉ ghi đè nếu RTU không tắt coil
        {
          digitalWrite(output[i], LOW); // Tắt GPIO từ TCP
        }
      }

      // Debug từng giá trị coil từ TCP
      // Serial.print("TCP Coil ");
      // Serial.print(i);
      // Serial.print(": ");
      // Serial.println(mb_tcp.Coil(i));
    }

    // Quay lại trạng thái đọc dữ liệu từ các slave
    state = 0;
    break;
  }

  // Xử lý Modbus task
  mb_tcp.task();
  mb.task();
  yield();
}

void writeHregToSlave11(int reg, uint16_t value)
{
  if (mb.writeHreg(11, reg, value))
  {
    mb.task();
    // Serial.println("Wrote to Slave 11 - Hreg" + String(reg) + ": " + String(value));
  }
  else
  {
    // Serial.println("Failed to write to Slave 11 - Hreg" + String(reg));
  }
}
