#include "RTU_SS.h"

void SensorModbus::begin(uint32_t baudRate, uint8_t txPin, uint8_t rxPin) {
    Serial2.begin(baudRate, SERIAL_8N1, txPin, rxPin);
    mb.begin(&Serial2);
    mb.master();
}

void SensorModbus::readData() {
    if (!mb.slave()) {
        if (mb.readHreg(2, 0, data, 5)) {
            temp = (float)data[0];
            humi = (float)data[1];
        }
    }
}

float SensorModbus::getTemperature() {
    return temp;
}

float SensorModbus::getHumidity() {
    return humi;
}
