
// Hàm đọc Coil từ RTU và đồng bộ với TCP
void syncRTUtoTCP()
{
  // Đọc coil từ RTU (slave 11)
  if (mb.readCoil(11, 0, coilState, 8)) // Đọc 8 coil từ địa chỉ 0 qua RTU
  {
    mb.task();

    for (int i = 0; i < 8; i++)
    {
      // Nếu RTU có thay đổi trạng thái coil
      if (coilState[i] != mb_tcp.Coil(i))
      {
        // Đồng bộ trạng thái từ RTU sang TCP
        mb_tcp.Coil(i, coilState[i]);
        Serial.print("RTU Coil ");
        Serial.print(i);
        Serial.println(" synced to TCP");
      }
    }
  }
  else
  {
    // Debug nếu đọc RTU coil thất bại
    Serial.println("Failed to read coils from RTU Slave 11");
  }
}

// Hàm đọc Coil từ TCP và đồng bộ với RTU
void syncTCPtoRTU()
{
  for (int i = 0; i < 8; i++)
  {
    // Nếu TCP có thay đổi trạng thái coil
    if (mb_tcp.Coil(i) != coilState[i])
    {
      bool success = mb.writeCoil(11, i, mb_tcp.Coil(i)); // Ghi coil xuống RTU
      mb.task();                                          // Xử lý giao tiếp

      if (success)
      {
        coilState[i] = mb_tcp.Coil(i); // Cập nhật trạng thái sau khi ghi thành công
        Serial.print("TCP Coil ");
        Serial.print(i);
        Serial.print(" synced to RTU with ");
        Serial.println(mb_tcp.Coil(i) ? "HIGH" : "LOW");
      }
      else
      {
        Serial.print("Failed to write TCP Coil ");
        Serial.println(i);
      }
    }
  }
}
