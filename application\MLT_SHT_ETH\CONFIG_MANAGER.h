#ifndef _CONFIG_MANAGER_
#define _CONFIG_MANAGER_
#define SSID_NAME "Smart_config"
#define EN_DEBUG
#if defined(EN_DEBUG)
#define debug Serial
#define DB(x) debug.print(x);
#define DB_LN(x) debug.println(x);
#define DB_BG(x) debug.begin(x);
#else
#define DB_BG(...)
#define DB(...)
#define DB_LN(...)
#endif

#define INTERNAL_CLOCK
#define RL1 2
#define RL2 4
#define RL4 5
#if defined INTERNAL_CLOCK
#define ETH_ADDR 1
#define ETH_POWER_PIN -1
#define ETH_MDC_PIN 23
#define ETH_MDIO_PIN 18
#define ETH_TYPE ETH_PHY_LAN8720
#define ETH_CLK_MODE ETH_CLOCK_GPIO17_OUT

#define TIME_CHECK_WIFI 2000

#define RL3 16

#endif

#if defined EXTERNAL_CLOCK
#define ETH_ADDR 1
#define ETH_POWER_PIN -1
#define ETH_POWER_PIN_ALTERNATIVE 16
#define ETH_MDC_PIN 23
#define ETH_MDIO_PIN 18
#define ETH_TYPE ETH_PHY_LAN8720
#define ETH_CLK_MODE ETH_CLOCK_GPIO0_IN
#define RL3 17

#endif

#include <WiFi.h>
#include <WiFiClient.h>
#include <WebServer.h>
#include <ESPmDNS.h>
#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <ESPmDNS.h>
#include <Update.h>
#include <EEPROM.h>
#include <ArduinoJson.h>
#include <AsyncElegantOTA.h>
#include <WiFiUdp.h>
#include <Arduino.h>
#include "FS.h"
#include <LITTLEFS.h>
#include <ETH.h>

AsyncWebServer server(80);
WiFiClient espClient;
WiFiUDP ntpUDP;
unsigned long timePolling;
String inputString;
bool stringComplete = false;
const char *ssid = "MLTECH_TANG1";
const char *password = "mltech@2019";
static bool eth_connected = false;
// Wifi configuration

const char *PARAM_ADDRESS = "address";
const char *PARAM_GETWAY = "getway";
const char *PARAM_SUBNET = "subnet";
const char *PARAM_PDNS = "p_dns";
const char *PARAM_SDNS = "s_dns";
struct ETHERNET
{
    String ipAdress = "*************";
    String getway = "***********";
    String subnet = "*************";
    String primaryDNS = "*******";
    String secondaryDNS = "*******";
} ethernet;
const char index_html[] PROGMEM = R"rawliteral(<!DOCTYPE html><html lang="en"><head><style>body{text-align:center;padding:0;margin:0}h1{text-align:center;text-shadow:#00f;color:#008b8b}button.menu{width:60%;background-color:#4caf50;color:#fff;padding:30px 20px;margin:8px 0;border:none;border-radius:20px;cursor:pointer}p.value{font-size:80% color:cadetblue}</style><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>cấu hình hệ thống</title></head><body><h1>CẤU HÌNH HỆ THỐNG</h1><div><form action="/ethernet" method="POST"><button class="menu">Thiết lập kết nối mạng</button></form></div><br><div><form action="/update" method="GET"><button class="menu">Cập nhập phần mềm</button></form></div><br><div><form action="/recovery" method="POST"><button class="menu">Khôi phục cài đặt</button></form></div><br><div><form action="/reset" method="POST"><button class="menu">Khởi động lại</button></form></div></body></html>
)rawliteral";
const char ethernet_html[] PROGMEM = R"rawliteral(<!DOCTYPE html><html lang="en"><head> <style>body{text-align: center;}h1{text-align: center; text-shadow: blue; color: darkcyan;}input[type=text], select{width: 60%; padding: 12px 20px; margin: 8px 0; display: inline-block; border: 1px solid #ccc; border-radius: 20px; box-sizing: border-box;}button[type=submit]{width: 60%; background-color: #4CAF50; color: white; padding: 14px 20px; margin: 8px 0; border: none; border-radius: 20px; cursor: pointer;}</style> <meta charset="UTF-8"> <meta http-equiv="X-UA-Compatible" content="IE=edge"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>cấu hình hệ thống</title></head><body> <h1>CẤU HÌNH WIFI</h1> <form action="/getIP"> <div><input type="text" name="address" size="30" placeholder="IP Address"></div><div><input type="text" name="getway" size="30" placeholder="Getway"></div><div><input type="text" name="subnet" size="30" placeholder="Subnet"></div><div><input type="text" name="p_dns" size="30" placeholder="Primary DNS"></div><div><input type="text" name="s_dns" size="30" placeholder="Secondary DNS"></div><br><div><button type="submit">Save</button></div></form></body></html>
)rawliteral";
const char reset_html[] PROGMEM = R"rawliteral(<!DOCTYPE html><html lang="en"><head> <style>body{text-align: center; padding: 0; margin: 0;}h1{text-align: center; text-shadow: blue; color: darkcyan;}button.menu{width: 60%; background-color: #4CAF50; color: white; padding: 30px 20px; margin: 8px 0px; border: none; border-radius: 20px; cursor: pointer;}</style> <meta charset="UTF-8"> <meta http-equiv="X-UA-Compatible" content="IE=edge"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>cấu hình hệ thống</title></head><body> <h1>RESETED!</h1> <div><a href="/"><button class="menu">Trở về trang chủ</button></div><br></html>
)rawliteral";
const char recovery_html[] PROGMEM = R"rawliteral(<!DOCTYPE html><html lang="en"><head><style>body{text-align:center;padding:0;margin:0}h1{text-align:center;text-shadow:#00f;color:#008b8b}button.menu{width:60%;background-color:#4caf50;color:#fff;padding:30px 20px;margin:8px 0;border:none;border-radius:20px;cursor:pointer}</style><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><title>cấu hình hệ thống</title></head><body><h1>Thành công!</h1><div><form action="/reset" method="POST"><button class="menu">Khởi động lại</button></form></div></body></html>
)rawliteral";
const char save_html[] PROGMEM = R"rawliteral(<!DOCTYPE html><html lang="en"><head> <style>body{text-align: center; padding: 0; margin: 0;}h1{text-align: center; text-shadow: blue; color: darkcyan;}button.menu{width: 60%; background-color: #4CAF50; color: white; padding: 30px 20px; margin: 8px 0px; border: none; border-radius: 20px; cursor: pointer;}</style> <meta charset="UTF-8"> <meta http-equiv="X-UA-Compatible" content="IE=edge"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>cấu hình hệ thống</title></head><body> <h1>ĐÃ LƯU</h1> <div><a href="/"><button class="menu">Trở về trang chủ</button></div><br><div><form action="/reset" method="POST"><button class="menu">Khởi động lại</button></form></div><br></html>
  )rawliteral";

IPAddress str2IP(String str);

void WiFiEvent(WiFiEvent_t event);
int getIpBlock(int index, String str);

void fileInit();
void writeFile(fs::FS &fs, const char *path, const char *message);
String readFile(fs::FS &fs, const char *path);
void createDir(fs::FS &fs, const char *path);
void saveLocalStorage();
void localStorageExport();
void managerInit();
void managerRun();
void managerInit()

{

    DB_BG(9600);
    fileInit();
    localStorageExport();
    WiFi.onEvent(WiFiEvent);

    // if (!WiFi.config(str2IP(ethernet.ipAdress), str2IP(ethernet.getway), str2IP(ethernet.subnet), str2IP(ethernet.primaryDNS), str2IP(ethernet.secondaryDNS)))
    // {
    //     Serial.println("STA Failed to configure");
    // }
    // WiFi.begin("Sunrise Gaming Club 2","123456789");

    ETH.begin(ETH_ADDR, ETH_POWER_PIN, ETH_MDC_PIN, ETH_MDIO_PIN, ETH_TYPE, ETH_CLK_MODE);
    ETH.config(str2IP(ethernet.ipAdress), str2IP(ethernet.getway), str2IP(ethernet.subnet), str2IP(ethernet.primaryDNS), str2IP(ethernet.secondaryDNS));

    server.on("/", HTTP_GET, [](AsyncWebServerRequest *request)
              { request->send_P(200, "text/html", index_html); });
    server.on("/ethernet", [](AsyncWebServerRequest *request)
              {
        DB_LN("setup ethernet");
        request->send_P(200, "text/html", ethernet_html); });
    server.on("/reset", [](AsyncWebServerRequest *request)
              {
        DB_LN("reset");
        request->send(200, "text/html", reset_html);
        delay(500);
        ESP.restart(); });
    server.on("/recovery", [](AsyncWebServerRequest *request)
              {
                  DB_LN("recovery");
                  request->send(200, "text/html", recovery_html);
                  ethernet.ipAdress = "*************";
                  ethernet.getway = "***********";
                  ethernet.subnet ="*************";
                 ethernet.primaryDNS="*******";
                 ethernet.secondaryDNS="*******";
                 saveLocalStorage(); });

    server.on("/getIP", [](AsyncWebServerRequest *request)
              {
                  String inputMessage;
                  if (request->hasParam(PARAM_ADDRESS))
                  {
                      ethernet.ipAdress = request->getParam(PARAM_ADDRESS)->value();

                      DB_LN("IP ADDRESS: " + (String)ethernet.ipAdress);
                  }
                  if (request->hasParam(PARAM_GETWAY))
                  {
                      ethernet.getway = request->getParam(PARAM_GETWAY)->value();

                      DB_LN("GETWAY: " + (String)ethernet.getway);
                  }
                  if (request->hasParam(PARAM_SUBNET))
                  {
                      ethernet.subnet = request->getParam(PARAM_SUBNET)->value();

                      DB_LN("Subnet: " + (String)ethernet.subnet);
                  }
                  if (request->hasParam(PARAM_PDNS))
                  {
                      ethernet.primaryDNS = request->getParam(PARAM_PDNS)->value();

                      DB_LN("Primary DNS " + (String)ethernet.primaryDNS);
                  }
                  if (request->hasParam(PARAM_SDNS))
                  {
                      ethernet.secondaryDNS = request->getParam(PARAM_SDNS)->value();

                      DB_LN("Secondary DNS " + (String)ethernet.secondaryDNS);
                  }

                  request->send(200, "text/html", save_html);
                  saveLocalStorage(); });
    AsyncElegantOTA.begin(&server);
    server.begin();
}
void managerRun()
{
    static unsigned long preTime_2 = millis();
    if (eth_connected == false && (millis() - preTime_2 > TIME_CHECK_WIFI) && (WiFi.status() != WL_CONNECTED))
    {
        Serial.println("WiFi connecting...");
        preTime_2 = millis();
    }
    if (eth_connected == true)
    {
        WiFi.disconnect();
    }
}
void WiFiEvent(WiFiEvent_t event)
{
    switch (event)
    {
    case SYSTEM_EVENT_ETH_START:
        Serial.println("ETH Started");
        // set eth hostname here
        ETH.setHostname("esp32-ethernet");
        break;
    case SYSTEM_EVENT_ETH_CONNECTED:
        Serial.println("ETH Connected");
        break;
    case SYSTEM_EVENT_ETH_GOT_IP:
        Serial.print("ETH MAC: ");
        Serial.print(ETH.macAddress());
        Serial.print(", IPv4: ");
        Serial.print(ETH.localIP());
        if (ETH.fullDuplex())
        {
            Serial.print(", FULL_DUPLEX");
        }
        Serial.print(", ");
        Serial.print(ETH.linkSpeed());
        Serial.println("Mbps");
        eth_connected = true;
        break;
    case SYSTEM_EVENT_ETH_DISCONNECTED:
        Serial.println("ETH Disconnected");
        eth_connected = false;
        break;
    case SYSTEM_EVENT_ETH_STOP:
        Serial.println("ETH Stopped");
        eth_connected = false;
        break;
    default:
        break;
    }
}

IPAddress str2IP(String str)
{
    IPAddress ret(getIpBlock(0, str), getIpBlock(1, str), getIpBlock(2, str), getIpBlock(3, str));
    return ret;
}
int getIpBlock(int index, String str)
{
    char separator = '.';
    int found = 0;
    int strIndex[] = {0, -1};
    int maxIndex = str.length() - 1;

    for (int i = 0; i <= maxIndex && found <= index; i++)
    {
        if (str.charAt(i) == separator || i == maxIndex)
        {
            found++;
            strIndex[0] = strIndex[1] + 1;
            strIndex[1] = (i == maxIndex) ? i + 1 : i;
        }
    }
    return found > index ? str.substring(strIndex[0], strIndex[1]).toInt() : 0;
}

void fileInit()
{
    if (!LITTLEFS.begin(true))
    {
        Serial.println("LITTLEFS Mount Failed");
        return;
    }
}
void writeFile(fs::FS &fs, const char *path, const char *message)
{
    Serial.printf("Writing file: %s\r\n", path);

    File file = fs.open(path, FILE_WRITE);
    if (!file)
    {
        Serial.println("- failed to open file for writing");
        return;
    }
    if (file.print(message))
    {
        Serial.println("- file written");
    }
    else
    {
        Serial.println("- write failed");
    }
    file.close();
}
String readFile(fs::FS &fs, const char *path)
{
    String data = "";
    Serial.printf("Reading file: %s\r\n", path);

    File file = fs.open(path);
    if (!file || file.isDirectory())
    {
        Serial.println("- failed to open file for reading");
        createDir(LITTLEFS, "/data");
        saveLocalStorage();
    }

    Serial.println("- read from file:");
    while (file.available())
    {
        data += (char)file.read();
    }
    file.close();
    return data;
}
void createDir(fs::FS &fs, const char *path)
{
    Serial.printf("Creating Dir: %s\n", path);
    if (fs.mkdir(path))
    {
        Serial.println("Dir created");
    }
    else
    {
        Serial.println("mkdir failed");
    }
}
void saveLocalStorage()
{
    DynamicJsonDocument data(1024);
    data[PARAM_ADDRESS] = ethernet.ipAdress;
    data[PARAM_GETWAY] = ethernet.getway;
    data[PARAM_SUBNET] = ethernet.subnet;
    data[PARAM_PDNS] = ethernet.primaryDNS;
    data[PARAM_SDNS] = ethernet.secondaryDNS;
    String objectString;
    serializeJson(data, objectString);
    writeFile(LITTLEFS, "/data/localStorage.json", objectString.c_str());
    Serial.println(objectString);
}
void localStorageExport()
{
    String data = readFile(LITTLEFS, "/data/localStorage.json");
    Serial.println(data);
    StaticJsonDocument<512> doc;
    DeserializationError error = deserializeJson(doc, data.c_str());
    if (error)
    {
        Serial.println("deserializeJson failed");
        Serial.println(error.f_str());
        return;
    }
    else
    {
        ethernet.ipAdress = doc[PARAM_ADDRESS].as<String>();
        ethernet.getway = doc[PARAM_GETWAY].as<String>();
        ethernet.subnet = doc[PARAM_SUBNET].as<String>();
        ethernet.primaryDNS = doc[PARAM_PDNS].as<String>();
        ethernet.secondaryDNS = doc[PARAM_SDNS].as<String>();
        Serial.println("Export data success!!");
    }
}

void wifiConnect()
{
    static unsigned long preTime = millis();
    if ((millis() - preTime > TIME_CHECK_WIFI) && (WiFi.status() != WL_CONNECTED))
    {
        Serial.println("WiFi connecting...");
        preTime = millis();
    }
}
#endif