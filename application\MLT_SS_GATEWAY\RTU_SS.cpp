#include "RTU_SS.h"
// #include <ModbusEthernet.h>

// Đ<PERSON><PERSON> tượng ModbusEthernet
// extern ModbusEthernet mb;

// Đ<PERSON><PERSON> tượng ModbusRTU
ModbusRTU mb_rtu;

// Biến toàn cục
float temp;
float humi;
float light;
uint16_t data[10]; // Mảng chứa dữ liệu đọc từ Modbus RTU

bool RTU_read()
{
    if (mb_rtu.readHreg(1, 500, data, 10))
    {
        temp  = data[1]; // 501
        humi  = data[0]; // 500

        if (data[7] < 60000)
        {
            light = data[7];
        }
        else
        {
            light = ((uint32_t)(data[7] - 60000) * 65536UL) + data[8];
        }

        return true;
    }
    else
    {
        Serial.println("Lỗi đọc RTU Hreg tại địa chỉ 500!");
        return false;
    }
}


// void taskReadSlave()
// {
//     if (RTU_read())
//     {
//         // Ghi dữ liệu và<PERSON> các thanh ghi Input cho Modbus TCP
//         mb.Ireg(1, light);
//         mb.Ireg(2, humi);
//         mb.Ireg(3, temp);
//     }
// }
