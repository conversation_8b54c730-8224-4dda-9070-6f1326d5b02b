#include <Arduino.h>
#include <ModbusRTU.h>
// #include <ModbusEthernet.h>
#include "RTU_SS.h" // G<PERSON><PERSON> thư viện RTU

// ModbusEthernet mb;

void setup()
{
    Serial.begin(9600);

    Serial2.begin(9600, SERIAL_8N1);
    mb_rtu.begin(&Serial2);
    mb_rtu.master();

    // mb.server(); // Khởi động Modbus TCP
}

void loop()
{
    mb_rtu.task();
    // mb.task();

    static uint32_t lastTime = 0;
    if (millis() - lastTime > 1000)
    {
        lastTime = millis();

        if (RTU_read()) // Nếu đọc RTU thành công
        {
            // mb.Ireg(1, light);
            // mb.Ireg(2, humi);
            // mb.Ireg(3, temp);

            // In ra Serial
            Serial.print("Temp: ");
            Serial.print(temp/10);
            Serial.print(" | Humi: ");
            Serial.print(humi/10);
            Serial.print(" | Light: ");
            Serial.println(light);
        }
    }
}
