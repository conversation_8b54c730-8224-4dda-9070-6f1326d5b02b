#include <WiFi.h>
#include <ModbusIP_ESP8266.h>   // Dùng cho ESP32 cũng được
#include <ModbusRTU.h>

// ==== WiFi cấu hình ====
const char* ssid = "DalatTech";
const char* password = "mltech@2019";
IPAddress ip(192, 168, 1, 50);
IPAddress gateway(192, 168, 1, 1);
IPAddress subnet(255, 255, 255, 0);

// ==== Modbus ====
ModbusIP mbTCP;       // Modbus TCP server
ModbusRTU mbRTU;      // RTU master

#define SLAVE_ID 1
#define COIL_COUNT 100
bool coils[COIL_COUNT] = {0};

void setup() {
  Serial.begin(9600);
  Serial2.begin(9600, SERIAL_8N1);      // RS485 giao tiếp RTU
  mbRTU.begin(&Serial2);
  mbRTU.master();

  // Kết nối WiFi IP tĩnh
  WiFi.config(ip, gateway, subnet);
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\n✅ WiFi connected: " + WiFi.localIP().toString());
  // Khởi tạo Modbus TCP Server
  mbTCP.server();               // mở cổng 502
  mbTCP.addCoil(0, coils, COIL_COUNT);  // map vùng coil
}

void loop() {
  mbTCP.task();   // Xử lý TCP
  mbRTU.task();   // Xử lý RTU master

  static uint32_t lastSent = 0;
  if (millis() - lastSent > 500) {
    lastSent = millis();
    mbRTU.writeCoil(SLAVE_ID, 0, coils, COIL_COUNT); // ghi 16 coil xuống slave
  }
}
