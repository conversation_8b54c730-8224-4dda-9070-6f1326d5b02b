#include "Arduino.h"
#include <ModbusRTU.h>
#include "MLBoard8CH.h"
MLBoard8CH *board = new MLBoard8CH();

ModbusRTU mb;
void setup()
{
    Serial.begin(115200);
    Serial2.begin(9600);
    board->begin();
    mb.begin(&Serial2);
    mb.slave(1);
    mb.addIsts(0, 0, 8);
    mb.addCoil(0, 0, 10);
}
void loop()
{
    // board->testInput();
    for (int i = 0; i < 8; i++)
    {
        mb.Ists(i, digitalRead(input[i]));
        digitalWrite(output[i], mb.Coil(i));
    }
    mb.task();
    yield();
    // delay(100);
}