#include "Arduino.h"
#include "ETH.h"
#include "ModbusRTU.h"
#include <ModbusIP_ESP8266.h>
#include <Arduino.h>
#include <HTTPClient.h>
#include "WiFi.h"
#include "PubSubClient.h"
#include "ArduinoJson.h"
#include "time.h"
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
// #include <AsyncElegantOTA.h>
#include "FS.h"
#include <LITTLEFS.h>
#include "NTPClient.h"
#include "WiFiUdp.h"
////////////////////////////////////////////////////
#define THINGS_BOARD_SERVER "mqtt.viis.tech"
// #define TOKEN "VmNkJ3v5DNwho5NCU1zK"
// #define ID "ec40d1c0-61d6-11ed-99cb-4de8ebde04d6"
#define TOKEN "b06b554b-2424-400e-afa7-e20e994945a2"
#define ID "c3e9ef80-7634-11ef-930e-0d87266f9a7d"
#define TIME_CHECK_WIFI 2000
#define TIME_SEND_SS 1000
////////////////////////////////////////////////////
/////////////////////////////////////////////////////
WiFiClient espClient;
PubSubClient client(espClient);
String serverName = "https://iot.viis.tech/api/viis/schedule/device?accessToken=";
AsyncWebServer server(80);
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, "pool.ntp.org", 7 * 60 * 60);
// const char *ssid = "Farm";
// const char *pass = "mltech@2019";
const char *ssid = "MLTECH";
const char *pass = "mltech@2019";

ModbusRTU mb;
uint16_t data[20];
float temp;
float humi;
float co2;

void wifiConnect();
void mqtt_sendTelemertry();
void sendTelemertry(String key, bool value);
void saveLocalStorage();
void localStorageExport();
void mqttInit();
void on_message(const char *topic, byte *payload, unsigned int length);
void mqtt_loop();
void mqtt_reconnect();

void setup()
{
    Serial.begin(9600);

    WiFi.begin(ssid, pass);
    mqttInit();
    while (WiFi.status() != WL_CONNECTED)
    {
        Serial.println("wificonnect...");
        Serial.print(".");
    }

    Serial.println("");
    Serial.println("WiFi connected");
    Serial.println("IP address: ");
    Serial.println(WiFi.localIP());

    Serial2.begin(9600);
    mb.begin(&Serial2);
    mb.master();
}

bool coils[20];

void loop()
{
    mqtt_loop();
    static unsigned long lastReadTime = millis();
    if (millis() - lastReadTime >= 1000)
    {
        if (!mb.slave())
        {
            mb.readIreg(2, 0, data, 5);
            temp = (float)data[0];
            humi = (float)data[1];
            co2 = (float)data[2];
        }
        Serial.println("temp1: " + String(temp));
        Serial.println("humi1: " + String(humi));
        Serial.println("co2: " + String(co2));

        mb.task();
        yield();
        lastReadTime = millis(); // Cập nhật thời gian đọc lần cuối
    }
    mqtt_sendTelemertry();
}

void wifiConnect()
{
    static unsigned long preTime = millis();
    if ((millis() - preTime > TIME_CHECK_WIFI) && (WiFi.status() != WL_CONNECTED))
    {
        Serial.println("WiFi connecting...");
        preTime = millis();
    }
}

void mqttInit()
{
    client.setServer(THINGS_BOARD_SERVER, 1883);
    client.setCallback(on_message);
}

void on_message(const char *topic, byte *payload, unsigned int length)
{
    StaticJsonDocument<1024> doc;
    Serial.println("On message");
    char json[length + 1];
    strncpy(json, (char *)payload, length);
    json[length] = '\0';
    Serial.println("TOPIC: " + (String)topic);
    Serial.println("Message: " + (String)json);
    DeserializationError error = deserializeJson(doc, json);
    if (error)
    {
        Serial.println("deserializeJson failed");
        Serial.println(error.f_str());
        return;
    }
    if (strstr((char *)payload, "set_state") != NULL)
    {
        // writeFile(LITTLEFS, "/data/localStorage.json", json);
    }
    else if (strstr((char *)payload, "update_schedule") != NULL)
    {
        // writeFile(LittleFS,httpGETRequest(serverName.c_str()).c_str())
        // Serial.println(httpGETRequest(serverName.c_str()));
        // jsonObjectTimer(httpGETRequest(serverName.c_str()));
    }
    String responseTopic = String(topic);
    responseTopic.replace("request", "response");
    Serial.println(responseTopic.c_str());
    // client.publish(responseTopic.c_str(),);
}

void mqtt_loop()
{
    if (!client.connected())
    {
        mqtt_reconnect();
    }

    client.loop();
}
void mqtt_reconnect()
{
    if ((!client.connected()))
    {
        Serial.println("Connecting to thingsboard...");
        if (client.connect(ID, TOKEN, NULL))
        {
            Serial.println("Connected");
            client.subscribe("v1/devices/me/rpc/request/+");
            // client.subscribe("v1/devices/me/attributes/request/+");
            client.subscribe("v1/devices/me/attributes");

            mqtt_sendTelemertry();
        }
        else
        {
            Serial.println("Connect fail");
            Serial.println(client.state());

            //  delay(2000);
        }
    }
}
void mqtt_sendTelemertry()
{
    static unsigned long lastTime = millis();
    if (millis() - lastTime >= 1000)
    {
        DynamicJsonDocument data(1024);
        // data["state"]=digitalRead(Q1);
        data["temp"] = temp;
        data["humi"] = humi;
        data["co2"] = co2;

        String objectString;
        serializeJson(data, objectString);
        //  Serial.println(objectString);
        client.publish("v1/devices/me/telemetry", objectString.c_str());

        lastTime = millis();
    }
}
void sendTelemertry(String key, bool value)
{
    DynamicJsonDocument data(1024);
    value == true ? data[key] = true : data[key] = false;
    String objectString;
    serializeJson(data, objectString);
    client.publish("v1/devices/me/telemetry", objectString.c_str());
    Serial.println(objectString);
}

String httpGETRequest(const char *serverName)
{
    String payload;
    WiFiClientSecure client;
    client.setInsecure();
    HTTPClient http;

    String serverPath = serverName + (String)TOKEN;

    // Your Domain name with URL path or IP address with path
    http.begin(client, serverPath.c_str());

    // Send HTTP GET request
    int httpResponseCode = http.GET();

    if (httpResponseCode > 0)
    {
        Serial.print("HTTP Response code: ");
        Serial.println(httpResponseCode);
        payload = http.getString();
        Serial.println(payload);
    }
    else
    {
        Serial.print("Error code: ");
        Serial.println(httpResponseCode);
    }
    // Free resources
    http.end();

    return payload;
}