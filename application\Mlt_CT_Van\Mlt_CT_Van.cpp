#include "Arduino.h"
#include <ModbusRTU.h>

ModbusRTU mb;  // Tạo đối tượng ModbusRTU

bool cbWrite(Modbus::ResultCode event, uint16_t transactionId, void* data) {
  Serial.printf("Request result: 0x%02X, TransactionID: %d\n", event, transactionId);
  return true;
}

void setup() {
  Serial.begin(115200);  // Serial để debug
  Serial2.begin(9600, SERIAL_8N1, 16, 17);  // Serial2 với chân RX là IO16 và TX là IO17
  mb.begin(&Serial2);  // Khởi tạo giao tiếp Modbus qua Serial2
  mb.master();  // Thiết lập ESP32 làm master
}

void loop() {
  // Vòng lặp ghi các coil từ 0 đến 16 (coil có địa chỉ 1 đến 16)
  for (int i = 0; i < 16; i++) {
    // Bật coil
    bool coilStateOn = true;  // Gi<PERSON> trị muốn ghi cho các coil (bật)
    mb.writeCoil(2, i, coilStateOn, cbWrite);  // Ghi coil tại slave có địa chỉ 1, coil địa chỉ i
    delay(1000);  // Chờ 1 giây

    // Tắt coil
    bool coilStateOff = false;  // Giá trị muốn ghi cho các coil (tắt)
    mb.writeCoil(1, i, coilStateOff, cbWrite);  // Ghi coil tại slave có địa chỉ 1, coil địa chỉ i
    delay(1000);  // Chờ 1 giây
  }
  
  mb.task();  // Thực thi nhiệm vụ Modbus
}
