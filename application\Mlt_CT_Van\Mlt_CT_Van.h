#include "SmartConfig.h"
#include "ParingApiServer.h"
#include "NetworkService.h"
#include "DeviceInfor.h"
#include "ThingsboardService.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"
#include "OTAService.h"
#include "ScheduleService.h"
#include "ML_NTP.h"
#include <ModbusRTU.h>

bool pre_output[8] = {false};

ML_NTP *ntp = new ML_NTP();
hw_timer_t *Timer_1s = NULL;
ModbusRTU mb;  // <PERSON><PERSON><PERSON> tượng Modbus

unsigned long timeUpdateRTC = 1000;
typedef struct
{
    int timer_for_schedule = 0;
    int timer_update_in_out = 0;
} timer_count_t;

timer_count_t timer_count;

void IRAM_ATTR Timer0_ISR();
void timer1sInit();
void on_message(char *topic, uint8_t *payload, unsigned int length);
void updateInOut();
void updateThingsboardState();
void updateThingsboardByEvent();
void updateShedule();

void setup();
void loop();
