#include <Arduino.h>
#include <LiquidCrystal_I2C.h>

// Đ<PERSON>nh nghĩa các chân relay
#define RELAY1_PIN 2
#define RELAY2_PIN 4
#define RELAY3_PIN 5
#define RELAY4_PIN 12

// Đ<PERSON><PERSON> nghĩa các chân input
#define INPUT1_PIN 18
#define INPUT2_PIN 19
#define INPUT3_PIN 34
#define INPUT4_PIN 35

// Định nghĩa chân analog input
#define POT_PIN 36

#define NUM_READINGS 10 // Số lần đọc để tính trung bình

int readings[NUM_READINGS]; // Mảng lưu trữ các giá trị đọc
int readIndex = 0; // Chỉ số hiện tại của mảng
int total = 0; // Tổng của các giá trị đọc
int average = 0; // Giá trị trung bình
int timer;
int previousTimer = -1; // Biến để lưu trữ giá trị trước đó của timer

// Biến để lưu trữ thời gian bật relay
unsigned long relay1OnTime = 0;
unsigned long relay2OnTime = 0;
unsigned long relay3OnTime = 0;
unsigned long relay4OnTime = 0;

// Biến để lưu trạng thái của relay
bool relay1State = false;
bool relay2State = false;
bool relay3State = false;
bool relay4State = false;

// Biến để lưu trạng thái trước đó của input
bool previousInput1State = LOW;
bool previousInput2State = LOW;
bool previousInput3State = LOW;
bool previousInput4State = LOW;

// Khởi tạo đối tượng LCD
LiquidCrystal_I2C lcd(0x27, 20, 4); // Địa chỉ I2C của LCD là 0x27, 20x4 ký tự

void setup() {
  Serial.begin(9600);

  pinMode(RELAY1_PIN, OUTPUT);
  pinMode(RELAY2_PIN, OUTPUT);
  pinMode(RELAY3_PIN, OUTPUT);
  pinMode(RELAY4_PIN, OUTPUT);

  pinMode(INPUT1_PIN, INPUT);
  pinMode(INPUT2_PIN, INPUT);
  pinMode(INPUT3_PIN, INPUT);
  pinMode(INPUT4_PIN, INPUT);

  pinMode(POT_PIN, INPUT);

  digitalWrite(RELAY1_PIN, LOW);
  digitalWrite(RELAY2_PIN, LOW);
  digitalWrite(RELAY3_PIN, LOW);
  digitalWrite(RELAY4_PIN, LOW);

  lcd.init();
  lcd.backlight();

  for (int i = 0; i < NUM_READINGS; i++) {
    readings[i] = 0;
  }
}

void loop() {
  int input1State = digitalRead(INPUT1_PIN);
  int input2State = digitalRead(INPUT2_PIN);
  int input3State = digitalRead(INPUT3_PIN);
  int input4State = digitalRead(INPUT4_PIN);

  total -= readings[readIndex];
  readings[readIndex] = analogRead(POT_PIN);
  total += readings[readIndex];
  readIndex = (readIndex + 1) % NUM_READINGS;
  average = total / NUM_READINGS;

  timer = map(average, 0, 4095, 1, 60);

  const unsigned long relayDuration = timer * 1000;

  if (timer != previousTimer) {
    lcd.clear();
    lcd.setCursor(0, 0);
    lcd.print("Time: " + String(timer) + " S");
    previousTimer = timer;
  }

  unsigned long currentTime = millis();

  // Kiểm tra và điều khiển relay 1
  if (input1State != previousInput1State) {
    if (input1State == HIGH) {
      if (!relay1State) {
        digitalWrite(RELAY1_PIN, HIGH);
        relay1OnTime = currentTime;
        relay1State = true;
        lcd.setCursor(0, 1);
        lcd.print("B1 ");
      } else {
        digitalWrite(RELAY1_PIN, LOW);
        relay1State = false;
        lcd.setCursor(0, 1);
        lcd.print("T1 ");
      }
    }
    previousInput1State = input1State;
  } else if (relay1State && currentTime - relay1OnTime >= relayDuration) {
    digitalWrite(RELAY1_PIN, LOW);
    relay1State = false;
    lcd.setCursor(0, 1);
    lcd.print("T1 ");
  }

  // Kiểm tra và điều khiển relay 2
  if (input2State != previousInput2State) {
    if (input2State == HIGH) {
      if (!relay2State) {
        digitalWrite(RELAY2_PIN, HIGH);
        relay2OnTime = currentTime;
        relay2State = true;
        lcd.setCursor(4, 1);
        lcd.print("B2 ");
      } else {
        digitalWrite(RELAY2_PIN, LOW);
        relay2State = false;
        lcd.setCursor(4, 1);
        lcd.print("T2 ");
      }
    }
    previousInput2State = input2State;
  } else if (relay2State && currentTime - relay2OnTime >= relayDuration) {
    digitalWrite(RELAY2_PIN, LOW);
    relay2State = false;
    lcd.setCursor(4, 1);
    lcd.print("T2 ");
  }

  // Kiểm tra và điều khiển relay 3
  if (input3State != previousInput3State) {
    if (input3State == HIGH) {
      if (!relay3State) {
        digitalWrite(RELAY3_PIN, HIGH);
        relay3OnTime = currentTime;
        relay3State = true;
        lcd.setCursor(8, 1);
        lcd.print("B3 ");
      } else {
        digitalWrite(RELAY3_PIN, LOW);
        relay3State = false;
        lcd.setCursor(8, 1);
        lcd.print("T3 ");
      }
    }
    previousInput3State = input3State;
  } else if (relay3State && currentTime - relay3OnTime >= relayDuration) {
    digitalWrite(RELAY3_PIN, LOW);
    relay3State = false;
    lcd.setCursor(8, 1);
    lcd.print("T3 ");
  }

  // Kiểm tra và điều khiển relay 4
  if (input4State != previousInput4State) {
    if (input4State == HIGH) {
      if (!relay4State) {
        digitalWrite(RELAY4_PIN, HIGH);
        relay4OnTime = currentTime;
        relay4State = true;
        lcd.setCursor(12, 1);
        lcd.print("B4 ");
      } else {
        digitalWrite(RELAY4_PIN, LOW);
        relay4State = false;
        lcd.setCursor(12, 1);
        lcd.print("T4 ");
      }
    }
    previousInput4State = input4State;
  } else if (relay4State && currentTime - relay4OnTime >= relayDuration) {
    digitalWrite(RELAY4_PIN, LOW);
    relay4State = false;
    lcd.setCursor(12, 1);
    lcd.print("T4 ");
  }
}
