#include "Arduino.h"
#include "ETH.h"
#include <Arduino.h>
#include <HTTPClient.h>
#include "WiFi.h"
#include "PubSubClient.h"
#include "ArduinoJson.h"
#include "time.h"
#include <WiFi.h>
#include <AsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <AsyncElegantOTA.h>
#include "FS.h"
#include <LITTLEFS.h>
#include "NTPClient.h"
#include "WiFiUdp.h"
#include <PID_v1.h>
#include <AutoPID.h>
#include <Wire.h>
#include <LiquidCrystal_I2C.h>
// #include <Adafruit_TCS34725.h>
#include "esp32-hal-ledc.h"

#include "SmartConfig.h"
#include "ThingsboardService.h"
#include "NetworkService.h"
#include "DeviceInfor.h"
#include "ParingApiServer.h"
#include "OTAService.h"

#define WDT_TIMEOUT 300

////////////////////////////////////////////////////
#define THINGS_BOARD_SERVER "mqtt.viis.tech"
#define TOKEN "b06b554b-2424-400e-afa7-e20e994945a2"
#define ID "c3e9ef80-7634-11ef-930e-0d87266f9a7d"
#define TIME_CHECK_WIFI 2000
#define TIME_SEND_SS 1000
////////////////////////////////////////////////////
/////////////////////////////////////////////////////

WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, "pool.ntp.org", 7 * 60 * 60);
const char *ssid = "MLtech";
const char *pass = "mltech@2019";
void tempCt();
void readSensor();
void readcolor();
void lcdInit();
void updateLCD();

void  mqtt_sendTelemertry();
void saveLocalStorage();
// void wifiConnect();
// bool eth_connected = false;
// void WiFiEvent(WiFiEvent_t event);
// void mqtt_sendTelemertry();
// void sendTelemertry(String key, bool value);
// void sendTelemertry(String key, float value);
// void sendTelemertry(String key, int value);
// void saveLocalStorage();
 void localStorageExport();
// void mqttInit();
void on_message(const char *topic, byte *payload, unsigned int length);
// void mqtt_loop();
// void mqtt_reconnect();
/*Local Storage API*/
// void fileInit();
void writeFile(fs::FS &fs, const char *path, const char *message);
String readFile2(fs::FS &fs, const char *path);
// void createDir(fs::FS &fs, const char *path);
//////////////////////////////
void selectChannel(uint8_t channel);
String weekDays[7] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};

// Month names
String months[12] = {"January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"};
const long gmtOffset_sec = +6 * 60 * 60;
const int daylightOffset_sec = 3600;
uint8_t number_of_timers = 0;

// //////////////////////////////
const int APin = 39;
int relay = 32;
float temperatureV;
float voltage;
float analogValue;
int timeupdate;
float settemp = 60;
float settime;
const int timedefault = 60000;
int measureValue(int measuredValue, int setValue)
{
    if (setValue == measuredValue || setValue == measuredValue + 1 || setValue == measuredValue - 1)
    {
        return settime * 1000; // Trả về timeset nếu giá trị set bằng giá trị đo được hoặc bằng ±2
    }
    else
    {
        return timedefault; // Trả về thời gian mặc định trong trường hợp khác
    }
}

//////////////////////////////////////////////
#define OUTPUT_MIN 0
#define OUTPUT_MAX 255
#define KP 10
#define KI 120
#define KD 30
#define EN_LCD
double temperatureA, setPoint, outputVal;
AutoPID myPID(&temperatureA, &setPoint, &outputVal, OUTPUT_MIN, OUTPUT_MAX, KP, KI, KD);
LiquidCrystal_I2C lcd(0x27, 16, 2);
#define PCA9548A_ADDRESS 0x70 // Địa chỉ của PCA9548A
#define NUM_SENSORS 5         // Số lượng cảm biến màu
// Adafruit_TCS34725 tcs[NUM_SENSORS] = {
//     Adafruit_TCS34725(TCS34725_INTEGRATIONTIME_50MS, TCS34725_GAIN_60X),
//     Adafruit_TCS34725(TCS34725_INTEGRATIONTIME_50MS, TCS34725_GAIN_60X),
//     Adafruit_TCS34725(TCS34725_INTEGRATIONTIME_50MS, TCS34725_GAIN_60X),
//     Adafruit_TCS34725(TCS34725_INTEGRATIONTIME_50MS, TCS34725_GAIN_60X),
//     Adafruit_TCS34725(TCS34725_INTEGRATIONTIME_50MS, TCS34725_GAIN_60X)};
uint16_t r[NUM_SENSORS];
uint16_t g[NUM_SENSORS];
uint16_t b[NUM_SENSORS];
uint16_t c[NUM_SENSORS];
uint16_t colorTemp[NUM_SENSORS];
struct timer
{
    int id;
    bool enable;
    String time;
    String interval;
    String keyName[11];
    bool state[11];
};
timer TIMER[100];
#define GY33_ADDRESS 0x29
void setup()
{
    Serial.begin(9600);
    Wire.begin(33, 25);
      esp_task_wdt_init(WDT_TIMEOUT, true);
esp_task_wdt_add(NULL);
    if (!SPIFFS.begin())
    {
        Serial.println("SPIFFS Mount Failed");
        return;
    }
      localStorageExport();
    DeviceInfor.init();
    NetworkService.init();
    SmartConfig.init();
    ParingApiServer.init();
    ThingsboardService.init(on_message);
    ThingsboardService.connectMqtt();
    ThingsboardOTA.setDeviceToken(ThingsboardService.getDeviceTokent());
    ThingsboardOTA.init();

    pinMode(APin, INPUT);
    pinMode(relay, OUTPUT);
    lcdInit();
    myPID.setBangBang(0.1); // nếu nhiệt độ cao hơn or thấp hơn nhiệt độ set 0.3 thì sẽ bật or tắt pwm
    myPID.setTimeStep(1000);
    WiFi.begin(ssid, pass);
    for (uint8_t count_connect_wifi = 0; count_connect_wifi < 5; count_connect_wifi++)
    {
        if (WiFi.status() != WL_CONNECTED)
        {
            Serial.println("Failed to connect wifi!");
        }
        else
        {
            Serial.println("Connect wifi success!");
            break;
        }
    }
    // Khởi tạo các cảm biến màu sắc
    for (uint8_t i = 0; i < NUM_SENSORS; i++)
    {
        selectChannel(i);
        // if (!tcs[i].begin())
        // {
        //     Serial.println("Failed to initialize TCS34725 sensor!");
        //     // while (1);
        // }
        Serial.print("Sensor ");
        Serial.print(i);
        Serial.println(" initialized");
    }
    // Calib ban đầu cảm biến màu sắc
    readcolor();
  //  writeFile(LITTLEFS, "/data/config.json", "hello");
    // Serial.println(readFile(LITTLEFS, "/data/config.json"));
}
void loop()
{
  esp_task_wdt_reset();
    NetworkService.loop();
    SmartConfig.loop();
    ThingsboardService.loop();
    if (ThingsboardService.needReconnect())
    {

        ThingsboardService.connectMqtt();
    }
    if (ThingsboardService.getEventConnected())
    {
        ThingsboardService.removeEventConncted();
    }



    updateLCD();
    readSensor();
    readcolor();
    tempCt();
    mqtt_sendTelemertry();
    //   if (eth_connected || WL_CONNECTED)
    //   {
    //     mqtt_loop();
    //   }
    // NTPTimeUpdate();
}
void tempCt()
{
    setPoint = settemp;
    myPID.run();
    ledcWrite(relay, (uint16_t)outputVal);
    // //Serial.println(outputVal);
}

void readSensor()
{
    analogValue = analogRead(APin);
    voltage = analogValue * (3.3 / 4095.0);                   // Chuyển đổi giá trị analog sang điện áp (0-3.3V)
    temperatureV = map(voltage, 0.0, 3.3, -20.0, 105.0);      // Ánh xạ điện áp vào phạm vi nhiệt độ (-20 đến 105 độ C)
    temperatureA = map(analogValue, 0.0, 4095.0, 0.0, 105.0); // Ánh xạ điện áp vào phạm vi nhiệt độ (-20 đến 105 độ C)
                                                              // temperatureAC = temperatureA / 1.0202;
                                                              // Serial.println(analogValue);
}
void readcolor()
{
    static uint16_t offset_calib_red[NUM_SENSORS], offset_calib_green[NUM_SENSORS], offset_calib_blue[NUM_SENSORS];
    static uint8_t FIRSTTIME_CALIB_FLAG = 1;
    static unsigned long previousMillis = 0; // Biến cục bộ để lưu thời gian trước đó
    if (FIRSTTIME_CALIB_FLAG == 1)
    {
        if (millis() - previousMillis >= 200)
        {
            previousMillis = millis();
            // for (uint8_t i = 0; i < NUM_SENSORS; i++)
            // {
            //     selectChannel(i); // Chọn kênh
            //     tcs[i].getRawData(&r[i], &g[i], &b[i], &c[i]);
            //     colorTemp[i] = tcs[i].calculateColorTemperature_dn40(r[i], g[i], b[i], c[i]);
            //     char buffer[100];
            //     sprintf(buffer, "CALIB: Color Temp: %d K - Sensor %d: R= %d G= %d B= %d", colorTemp[0], i, r[i], g[i], b[i]);
            //     Serial.println(buffer);
            //     offset_calib_red[i] = r[i];
            //     offset_calib_green[i] = g[i];
            //     offset_calib_blue[i] = b[i];
            //     /////////////////////////////
            //     delay(1000);
            // }
        }
        FIRSTTIME_CALIB_FLAG = 0;
    }
    else
    {
        if (millis() - previousMillis >= 200)
        {
            previousMillis = millis();
            // for (uint8_t i = 0; i < NUM_SENSORS; i++)
            // {

            //     selectChannel(i); // Chọn kênh

            //     tcs[i].getRawData(&r[i], &g[i], &b[i], &c[i]);

            //     r[i] = ((int16_t)(r[i] - offset_calib_red[i]) < 0) ? 0 : (r[i] - offset_calib_red[i]);

            //     g[i] = ((int16_t)(g[i] - offset_calib_green[i]) < 0) ? 0 : (g[i] - offset_calib_green[i]);

            //     b[i] = ((int16_t)(b[i] - offset_calib_blue[i]) < 0) ? 0 : (b[i] - offset_calib_blue[i]);
            //     colorTemp[i] = tcs[i].calculateColorTemperature_dn40(r[i], g[i], b[i], c[i]);
            //     char buffer[100];
            //     sprintf(buffer, "Color Temp: %d K - Sensor %d: R= %d G= %d B= %d", colorTemp[0], i, r[i], g[i], b[i]);
            //     Serial.println(buffer);
            //     /////////////////////////////
            //     delay(1000);
            // }
        }
    }
}


void mqtt_sendTelemertry()
{
    static unsigned long lastTime = millis();

    timeupdate = measureValue(temperatureA, setPoint);
    // timeupdate = settime * 60000;
    if (millis() - lastTime >= timeupdate)
    {
        DynamicJsonDocument data(1024);
        data["temp"] = temperatureA;
        data["set_temp"] = settemp;
        data["set_time"] = settime;
        for (uint8_t i = 0; i < NUM_SENSORS; i++)
        {
            data["r" + String(i)] = r[i];
            data["g" + String(i)] = g[i];
            data["b" + String(i)] = b[i];
        }
        String objectString;
        serializeJson(data, objectString);
        ThingsboardService.sendTelemertry(objectString.c_str());
        //  //Serial.println(objectString);
        // client.publish("v1/devices/me/telemetry", objectString.c_str());
        lastTime = millis();
    }
}

void lcdInit()
{
    lcd.init();
    lcd.backlight();
    lcd.clear();
}
void updateLCD() // in dât ra màn hình theo 2 hàng ngang và tự clear màn hình sau 30s
{

    lcd.setCursor(0, 0);
    lcd.print("T1:" + (String)temperatureA);
    lcd.setCursor(0, 1);
    lcd.print("set:" + (String)settemp + "" + "t:" + (String)timeupdate);
    //+ " " + "time:" + (String)timeupdate
    static unsigned long preTime = millis();
    if (millis() - preTime >= 10000)
    {
        lcd.init();
        lcd.clear();
        preTime = millis();
    }
}

void selectChannel(uint8_t channel)
{
    // Chọn kênh trên PCA9548A
    Wire.beginTransmission(PCA9548A_ADDRESS);
    Wire.write(1 << channel);
    Wire.endTransmission();
}

void on_message(const char *topic, byte *payload, unsigned int length)
{
  StaticJsonDocument<1024> doc;
  // Serial.println("On message");
  char json[length + 1];
  strncpy(json, (char *)payload, length);
  json[length] = '\0';
  // Serial.println("TOPIC: " + (String)topic);
  // Serial.println("Message: " + (String)json);
  DeserializationError error = deserializeJson(doc, json);
  if (error)
  {
    // Serial.println("deserializeJson failed");
    // Serial.println(error.f_str());
    return;
  }
  if (strstr((char *)payload, "set_state") != NULL)
  {
    writeFile(LITTLEFS, "/data/localStorage.json", json);
    if (doc["params"].containsKey("set_temp"))
    {
      settemp = doc["params"]["set_temp"].as<int>();
      ThingsboardService.sendTelemertry("set_temp", settemp);
    }
    if (doc["params"].containsKey("set_time"))
    {
      settime = doc["params"]["set_time"].as<int>();
      ThingsboardService.sendTelemertry("set_time", settime);
    }
    saveLocalStorage();
  }
  else if (strstr((char *)payload, "update_schedule") != NULL)
  {
    // jsonObjectTimer(httpGETRequest(serverName.c_str()));
  }
  String responseTopic = String(topic);
  responseTopic.replace("request", "response");
  // Serial.println(responseTopic.c_str());
}

void saveLocalStorage()
{
  DynamicJsonDocument data(1024);
  data["set_temp"] = settemp;
  data["set_time"] = settime;
  String objectString;
  serializeJson(data, objectString);
  writeFile(LITTLEFS, "/data/localStorage.json", objectString.c_str());
  // Serial.println(objectString);
}
void localStorageExport()
{

    
String data = readFile2(LITTLEFS, "/data/localStorage.json");
  // Serial.println("READ FILE: " + (String)data);
  StaticJsonDocument<1024> doc;
  DeserializationError error = deserializeJson(doc, data.c_str());
  if (error)
  {
    // Serial.println("deserializeJson failed");
    // Serial.println(error.f_str());
    return;
  }
  else
  {
    settemp = doc["set_temp"];
    settime = doc["set_time"];
    // Serial.println("Export data success!!");
  }
}
String readFile2(fs::FS &fs, const char *path)
{
  String data = "";
  // Serial.printf("Reading file: %s\r\n", path);

  File file = fs.open(path);
  if (!file || file.isDirectory())
  {
    // Serial.println("- failed to open file for reading");
  }

  // Serial.println("- read from file:");
  while (file.available())
  {
    data += (char)file.read();
  }
  file.close();
  return data;
}
void createDir(fs::FS &fs, const char *path)
{
  // Serial.printf("Creating Dir: %s\n", path);
  if (fs.mkdir(path))
  {
    // Serial.println("Dir created");
  }
  else
  {
    // Serial.println("mkdir failed");
  }
}