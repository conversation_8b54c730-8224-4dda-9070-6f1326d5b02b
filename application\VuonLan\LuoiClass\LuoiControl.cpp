#include "LuoiControl.h"

LuoiControl::LuoiControl(uint8_t _pin_open, uint8_t _pin_close)
{
    pinClose = _pin_close;
    pinOpen = _pin_open;
}

void LuoiControl::writeState(int state)
{
    switch (state)
    {
    case 0: // CLOSE
        digitalWrite(pinOpen, false);
        if (digitalRead(pinOpen == false))
        {
            digitalWrite(pinClose, true);
        }
        break;
    case 1: // PAUSE
        digitalWrite(pinClose, false);
        digitalWrite(pinOpen, false);
        break;
    case 2://OPEN
    digitalWrite(pinClose,false);
    if(digitalRead(pinClose==false)){
        digitalWrite(pinOpen,true);
    }
        break;
    }
}

LuoiControl::~LuoiControl()
{
}