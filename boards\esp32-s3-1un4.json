{"build": {"arduino": {"ldscript": "esp32s3_out.ld", "memory_type": "qio_qspi"}, "core": "esp32", "extra_flags": ["-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DESP32S3_DEV", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0"], "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "mcu": "esp32s3", "variant": "esp32s3"}, "connectivity": ["wifi", "bluetooth"], "debug": {"openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>"], "name": "Espressif ESP32-S3", "upload": {"flash_size": "4MB", "maximum_ram_size": 327680, "maximum_size": 4194304, "require_upload_port": true, "speed": 921600}, "url": "https://www.adafruit.com/product/5290", "vendor": "E<PERSON>ress<PERSON>"}