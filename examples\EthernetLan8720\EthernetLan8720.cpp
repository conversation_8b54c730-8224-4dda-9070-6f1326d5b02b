
#include "ML_Ethernet.h"
#include "ModbusIP_ESP8266.h"
#include "ModbusRtuMaster.h"
#include "MLFs.h"
#include "Arduino.h"
#include "NetworkService.h"

ML_Ethernet *eth = new ML_Ethernet();
ML_ModbusRtuMaster *rtu=new ML_ModbusRtuMaster();
void setup()
{
    delay(5000);
Serial.begin(115200);
Serial2.begin(9600);
    eth->init();
    rtu->begin(&Serial2);

}
void loop()
{
uint16_t data[4];
 rtu->readHoldingRegisters(1,0,4,data);
for(int i=0;i<4;i++){
    Serial.print(data[i]);
    Serial.print(" : ");
}
Serial.println("---------");
}

