#include "MLBoard8CH.h"
#include "ModbusRtuMaster.h"
MLBoard8CH *ml_board=new MLBoard8CH();
ML_ModbusRtuMaster *rtuMaster=new ML_ModbusRtuMaster();
void testRs485();
void setup(){
Serial.begin(115200);
Serial2.begin(9600);
ml_board->begin();
rtuMaster->begin(&Serial2);
}
void loop(){
ml_board->testOutput();
testRs485();

}

void testRs485(){
uint16_t data_reg[4]={};
if(rtuMaster->readHoldingRegisters(1,0,4,data_reg)==READ_SUCCESS){
    for(int i=0;i<4;i++){
        Serial.print(data_reg[i]);
         Serial.print(" : ");
    }
    Serial.println("============");
}
else{
    Serial.println("READ ERRROR");
}
}