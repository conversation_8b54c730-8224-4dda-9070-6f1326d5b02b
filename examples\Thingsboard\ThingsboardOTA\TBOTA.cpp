#include "OTAService.h"
#include "Arduino.h"
#include "SPIFFS.h"
#include "ML_Debug.h"
#define WIFI_SSID "MLTECH_SHOP" // SET THIS!
#define WIFI_PASS "mltech@2019"     // SET THIS!
void initWiFi()
{
    WiFi.mode(WIFI_STA);
    WiFi.begin(WIFI_SSID, WIFI_PASS);

    Serial.println("Connecting to WiFi ..");
    while (WiFi.status() != WL_CONNECTED)
    {
        Serial.print('.');
        delay(1000);
    }
    Serial.println(WiFi.localIP());
}

void setup()
{
    delay(10000);
    Serial.begin(115200);
    if (!SPIFFS.begin(true))
    {
        Serial.println("An Error has occurred while mounting SPIFFS");
        return;
    }
    initWiFi();
    ThingsboardOTA.setDeviceToken("72MKn0NcxhyzB0hKgwwA");
    ThingsboardOTA.init();  
}

void loop()
{
ThingsboardOTA.loop();
}