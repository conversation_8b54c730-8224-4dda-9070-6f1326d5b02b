#include "Thingsboard_sample.h"
void setup()
{
    delay(5000);
    Serial.begin(115200);
    if (!SPIFFS.begin())
    {
        Serial.println("SPIFFS Mount Failed");
        return;
    }
    DeviceInfor.init();
    NetworkService.init();
    SmartConfig.init();
    ParingApiServer.init();
    ThingsboardService.init(on_message);
    ThingsboardService.connectMqtt();
    ThingsboardOTA.setDeviceToken(ThingsboardService.getDeviceTokent());
    Serial.println(ThingsboardService.getDeviceTokent());
    ThingsboardOTA.init();
}
void loop()
{
    NetworkService.loop();
    SmartConfig.loop();
    ThingsboardService.loop();
    ThingsboardOTA.loop();
    if (ThingsboardService.needReconnect())
    {

        ThingsboardService.connectMqtt();
    }
}
void on_message(char *topic, uint8_t *payload, unsigned int length)
{
    DB_PRINTLN("On message");
    StaticJsonDocument<1024> doc;
    char json[length + 1];
    strncpy(json, (char *)payload, length);
    json[length] = '\0';
    DB_PRINTLN("TOPIC: " + (String)topic);
    DB_PRINTLN("Message: " + (String)json);
    DeserializationError error = deserializeJson(doc, json);
    if (error)
    {
        DB_PRINTLN("deserializeJson failed");
        DB_PRINTLN(error.f_str());
        return;
    }
    if (strstr((char *)payload, "set_state") != NULL)
    {
    }
    if (strstr((char *)payload, "update_schedule") != NULL)
    {
    }

    String responseTopic = String(topic);
    responseTopic.replace("request", "response");
    DB_PRINTLN(responseTopic.c_str());
}