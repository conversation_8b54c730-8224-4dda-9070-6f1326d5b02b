#include "SmartConfig.h"
#include "ParingApiServer.h"
#include "NetworkService.h"
#include "DeviceInfor.h"
#include "ThingsboardService.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"
#include "OTAService.h"

hw_timer_t *Timer_1s = NULL;
void on_message(char *topic, uint8_t *payload, unsigned int length);
void IRAM_ATTR Timer0_ISR()
{

}
void timer1sInit()
{
    Timer_1s = timerBegin(0, 80, true);
    timerAttachInterrupt(Timer_1s, &Timer0_ISR, true);
    timerAlarmWrite(Timer_1s, 1000000, true);
    timerAlarmEnable(Timer_1s);
}

void setup();
void loop();