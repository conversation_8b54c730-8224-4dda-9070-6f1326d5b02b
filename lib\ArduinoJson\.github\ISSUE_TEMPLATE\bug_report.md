---
name: 🐛 Bug report
about: Report a bug in ArduinoJson
title: ''
labels: 'bug'
assignees: ''
---

<!-- ⚠️ IMPORTANT ⚠️
Before opening a bug report, please use the ArduinoJson Troubleshooter as it may find a solution to your issue; if not, please include the  Troubleshooter's report in the description.
-->

**Describe the bug**  
A clear and concise description of what the bug is.

**Troubleshooter report**  
Here is the report generated by the [ArduinoJson Troubleshooter](https://arduinojson.org/v6/troubleshooter/):  
[Paste the report here]

**Environment**  
Here is the environment that I used:
* Microcontroller: [e.g. ESP8266]
* Core/runtime: [e.g. ESP8266 core for Arduino v3.0.2]
* IDE: [e.g. Arduino IDE 1.8.16]

**Reproduction**  
Here is a small snippet that reproduces the issue.

```c++
DynamicJsonDocument doc(1024);

DeserializationError error = deserializeJson(doc, "{\"hello\":\"world\"}");

[insert repro code here]
```

**Compiler output**    
If relevant, include the complete compiler output (i.e. not just the line that contains the error.)


**Program output**  
If relevant, include the repro program output.

Expected output:

```
[insert expected output here]
```

Actual output:

```
[insert actual output here]
```
