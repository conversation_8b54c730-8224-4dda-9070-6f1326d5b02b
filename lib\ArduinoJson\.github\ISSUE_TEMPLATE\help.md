---
name: 😭 Help!
about: Ask for help
title: ''
labels: 'question'
assignees: ''
---

<!-- ⚠️ IMPORTANT ⚠️
Before asking for help, please use the ArduinoJson Troubleshooter as it may find a solution to your issue; if not, please include the  Troubleshooter's report in the description.
-->

**Describe the issue**  
A clear and concise description of what you're trying to do.
You don't need to explain every aspect of your project: focus on the problem you're having.

**Troubleshooter report**  
Here is the report generated by the [ArduinoJson Troubleshooter](https://arduinojson.org/v6/troubleshooter/):  
[Paste the report here]

**Environment**  
Here is the environment that I'm using':
* Microconroller: [e.g. ESP8266]
* Core/runtime: [e.g. ESP8266 core for Arduino v3.0.2]
* IDE: [e.g. Arduino IDE 1.8.16]

**Reproduction**  
Here is a small snippet that demonstrate the problem.

```c++
DynamicJsonDocument doc(1024);

DeserializationError error = deserializeJson(doc, "{\"hello\":\"world\"}");

// insert code here
```

**Program output**  
If relevant, include the program output.

Expected output:

```
[insert expected output here]
```

Actual output:

```
[insert actual output here]
```
