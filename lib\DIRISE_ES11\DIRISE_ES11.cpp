#include "DISE_ES11.h"
DISE_ES11 ML_ES11;
void DISE_ES11::begin(Stream *port, int deviceID)
{
    _port = port;
    _deviveID = deviceID;
    ML_RtuMaster.begin(_port);
}
void DISE_ES11::setFrequency(uint16_t Freq)
{
    static uint16_t pre_Freq = 5000;
    if (pre_Freq != Freq)
    {
        ML_RtuMaster.writeSingleRegister(_deviveID, OUTPUT_FREQ_REG, Freq);
        pre_Freq = Freq;
    }
}

uint16_t DISE_ES11::readFrequency()
{
    uint16_t Freq = 0;
    ML_RtuMaster.readHoldingRegisters(_deviveID, OUTPUT_FREQ_REG, 1, &Freq);
    return Freq;
}