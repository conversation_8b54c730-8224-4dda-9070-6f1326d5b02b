#include "DTB4848.h"
DTB4848 ML_DTB4848;
// DTB4848::DTB4848( ML_ModbusRtuMaster *modbus)
// {
// }
void DTB4848::begin(Stream *port, int ID)
{
    _port = port;
    _deviceID = ID;
    ML_RtuMaster.begin(_port);
    _control_state = getControl();
}
uint16_t DTB4848::getPV()
{
    uint16_t pv;
    ML_RtuMaster.readHoldingRegisters(_deviceID, PV_REG, 1, &pv);
    return pv;
}
uint16_t DTB4848::getSV()
{
    uint16_t sv;
    ML_RtuMaster.readHoldingRegisters(_deviceID, SV_REG, 1, &sv);
    return sv;
}
void DTB4848::setSV(uint16_t sv)
{
    static uint16_t _last_sv ;
    if (_last_sv != sv)
    {
         Serial.println("DELTA SP: "+(String)sv);
        ML_RtuMaster.writeSingleRegister(_deviceID, SV_REG, sv);
        _last_sv = getSV();
    }
}
bool DTB4848::getControl()
{
    bool control = false;
    ML_RtuMaster.readDiscreteInputs(_deviceID, CTRL_REG, 1, &control);
    return control;
}
void DTB4848::setControl(bool state)
{
    if (_control_state != state)
    {
        Serial.println("DELTA RUN: "+(String)state);
        ML_RtuMaster.writeSingleCoil(_deviceID, CTRL_REG, state);
        _control_state = getControl();
    }
}

void DTB4848::setATSetting(bool state)
{
    if (_at_setting_state != state)
    {
        ML_RtuMaster.writeSingleCoil(_deviceID, AT_REG, state);
        _at_setting_state=getATState();
    }
}
bool DTB4848::getATState()
{
    bool state = false;
    ML_RtuMaster.readDiscreteInputs(_deviceID, AT_REG, 1, &state);
    return state;
}