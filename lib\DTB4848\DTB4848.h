#ifndef _DTB4848_H_
#define _DTB4848_H_
#define PV_REG 0x1000
#define SV_REG 0x1001
#define CTRL_REG 0x814
#define AT_REG  0x813

#include "Arduino.h"
#include "ML_ModbusRtuMaster.h"


class DTB4848
{
private:
  Stream *_port;
  int _deviceID=1;
  bool _control_state=false;
  bool _at_setting_state=false;
  
public:

void begin(Stream *port,int ID);
uint16_t getPV();
uint16_t getSV();
void setSV(uint16_t sv);
bool getControl();
bool getATState();
void setControl(bool state);
void setATSetting(bool state);
};

extern DTB4848 ML_DTB4848;
#endif