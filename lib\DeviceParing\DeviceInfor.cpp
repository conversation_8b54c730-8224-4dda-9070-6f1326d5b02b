#include "DeviceInfor.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"

DeviceInforClass DeviceInfor;

void DeviceInforClass::init()
{
    this->loadInforFromFile();
}

bool DeviceInforClass::saveInforToFile(DeviceInforTypeDefStruct *deviceInfor)
{
    StaticJsonDocument<1024> doc;
    doc["serial_number"] = deviceInfor->serial_number;
    /* Save config to main file */
    DB_PRINTLN("Save config to file DEVICE_INFOR_PATH");

    File file = SPIFFS.open(DEVICE_INFOR_PATH, FILE_WRITE);
    if (!file)
    {
        DB_PRINTLN("Failed to open file DEVICE_INFOR_PATH");
        file.close();
        return false;
    }

    if (serializeJson(doc, file) == 0)
    {
        DB_PRINTLN("Failed to write to file DEVICE_INFOR_PATH");
        file.close();
        return false;
    }

    file.close();
    DB_PRINTLN("Success save config to file DEVICE_INFOR_PATH");
    this->infor.serial_number = deviceInfor->serial_number;

    return true;
}

bool DeviceInforClass::loadInforFromFile()
{
    StaticJsonDocument<512> doc;

    DB_PRINTLN("Read network config DEVICE_INFOR_PATH ");
    File file = SPIFFS.open(DEVICE_INFOR_PATH, FILE_READ);
    if (!file)
    {
        DB_PRINTLN("Failed to open file DEVICE_INFOR_PATH");
        return false;
    }

    DeserializationError error = deserializeJson(doc, file);
    if (error)
    {
        DB_PRINTLN("Failed to deserializeJson file");
        return false;
    }
    file.close();

    const char *serial_number = doc["serial_number"];

    this->infor.serial_number = String(serial_number);

    DB_PRINTLN("Success Read wifi infor to DEVICE_INFOR_PATH");

    return true;
}