#ifndef DEVICE_INFOR_H_
#define DEVICE_INFOR_H_

#include <Arduino.h>
#include "ML_Debug.h"
#define DEVICE_INFOR_PATH "/device_infor.json"

typedef struct DeviceInforTypeDefStruct
{
    String serial_number = "";
} DeviceInforTypeDefStruct;

class DeviceInforClass
{
private:
    DeviceInforTypeDefStruct infor;

public:
    void init();
    bool loadInforFromFile();
    bool saveInforToFile(DeviceInforTypeDefStruct *deviceInfor);
    String getSerialNumber() { return infor.serial_number; }
};

extern DeviceInforClass DeviceInfor;

#endif