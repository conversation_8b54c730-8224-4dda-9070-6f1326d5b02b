#include <Arduino.h>
#include "NetworkService.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"
#include "WiFi.h"

NetworkServiceClass NetworkService;

void NetworkServiceClass::init()
{
    this->loadWifiFromFile();
    this->addWiFiEvent();
    this->connectWifi();
}
bool NetworkServiceClass::saveWifiToFile(WifiInforTypeDefStruct *wifi_config_infor)
{
    StaticJsonDocument<1024> doc;
    JsonObject wifi = doc.createNestedObject("wifi");
    wifi["ssid"] = wifi_config_infor->ssid;
    wifi["password"] = wifi_config_infor->password;
    wifi["static_ip"] = wifi_config_infor->static_ip.toString();
    wifi["gateway_ip"] = wifi_config_infor->gateway_ip.toString();
    wifi["subnet_mask"] = wifi_config_infor->subnet_mask.toString();

    /* Save config to main file */
    DB_PRINTLN("Save config to file NETWORK_SERVICE_PATH");

    File file = SPIFFS.open(NETWORK_SERVICE_PATH, FILE_WRITE);
    if (!file)
    {
        DB_PRINTLN("Failed to open file NETWORK_SERVICE_PATH");
        file.close();
        return false;
    }

    if (serializeJson(doc, file) == 0)
    {
        DB_PRINTLN("Failed to write to file NETWORK_SERVICE_PATH");
        file.close();
        return false;
    }

    this->wifi_infor.ssid = wifi_config_infor->ssid;
    this->wifi_infor.password = wifi_config_infor->password;
    this->wifi_infor.static_ip = wifi_config_infor->static_ip;
    this->wifi_infor.gateway_ip = wifi_config_infor->gateway_ip;
    this->wifi_infor.subnet_mask = wifi_config_infor->subnet_mask;
    DB_PRINTLN("Success save config to file NETWORK_SERVICE_PATH");
    return true;
}

bool NetworkServiceClass::loadWifiFromFile()
{
    StaticJsonDocument<1024> doc;

    DB_PRINTLN("Read network config NETWORK_SERVICE_PATH ");
    File file = SPIFFS.open(NETWORK_SERVICE_PATH, FILE_READ);
    if (!file)
    {
        DB_PRINTLN("Failed to open file NETWORK_SERVICE_PATH");
        return false;
    }

    DeserializationError error = deserializeJson(doc, file);
    if (error)
    {
        DB_PRINTLN("Failed to deserializeJson file");
        return false;
    }
    file.close();

    JsonObject wifi = doc["wifi"];
    const char *wifi_ssid = wifi["ssid"];
    const char *wifi_password = wifi["password"];
    const char *wifi_static_ip = wifi["static_ip"];
    const char *wifi_gateway_ip = wifi["gateway_ip"];
    const char *wifi_subnet_mask = wifi["subnet_mask"];

    this->wifi_infor.ssid = String(wifi_ssid);
    this->wifi_infor.password = String(wifi_password);
    this->wifi_infor.static_ip.fromString(wifi_static_ip);
    this->wifi_infor.gateway_ip.fromString(wifi_gateway_ip);
    this->wifi_infor.subnet_mask.fromString(wifi_subnet_mask);
    DB_PRINTLN("Success Read wifi infor to NETWORK_SERVICE_PATH");

    return true;
}

void NetworkServiceClass::connectWifi()
{
    if (wifi_infor.ssid != NULL && wifi_infor.password != NULL)
    {
        // DB_PRINTLN("Connect Wifi STATIC_IP");
        // if (!WiFi.config(this->wifi_infor.static_ip, this->wifi_infor.gateway_ip, this->wifi_infor.subnet_mask))
        // {
        //     DB_PRINTLN("STA Failed to configure");
        // }
        WiFi.begin(wifi_infor.ssid.c_str(), wifi_infor.password.c_str());
    }

    else
    {
        DB_PRINTLN("Error wifi infor loaded from file --> begin WiFi without ssid, password");
        WiFi.begin();
    }
}

void NetworkServiceClass::loop()
{
    static uint32_t last_time_check_wifi = millis();
    static uint8_t reconnect_wifi_counter = 0;

    if ((uint32_t)(millis() - last_time_check_wifi > 5000))
    {
        if (WiFi.status() != WL_CONNECTED)
        {
            reconnect_wifi_counter++;
            if (reconnect_wifi_counter >= 3)
            {
                reconnect_wifi_counter = 0;
                this->connectWifi();
            }
        }
        else
        {
            reconnect_wifi_counter = 0;
        }

        last_time_check_wifi = millis();
    }
}

void NetworkServiceClass::addWiFiEvent()
{
    // Event got ip
    WiFi.onEvent([this](WiFiEvent_t event, WiFiEventInfo_t info)
                 {
                    DB_PRINTLN("WiFi connected");
                    DB_PRINTLN("IP address: ");
                    DB_PRINTLN(IPAddress(info.got_ip.ip_info.ip.addr));
                    DB_PRINTLN("Subnet address: ");
                    DB_PRINTLN(IPAddress(info.got_ip.ip_info.netmask.addr));
                    DB_PRINTLN("Default gateway address: ");
                    DB_PRINTLN(IPAddress(info.got_ip.ip_info.gw.addr)); 
                    this->wifi_infor.static_ip = IPAddress(info.got_ip.ip_info.ip.addr);
                    this->wifi_infor.gateway_ip = IPAddress(info.got_ip.ip_info.netmask.addr);
                    this->wifi_infor.subnet_mask= IPAddress(info.got_ip.ip_info.gw.addr); },
                 WiFiEvent_t::SYSTEM_EVENT_STA_GOT_IP);

    // Event disonnected
    WiFi.onEvent([](WiFiEvent_t event, WiFiEventInfo_t info)
                 {
                     DB_PRINT("WiFi lost connection. Reason: ");
                     DB_PRINTLN(info.disconnected.reason); },
                 WiFiEvent_t::SYSTEM_EVENT_STA_DISCONNECTED);
}

WifiInforTypeDefStruct NetworkServiceClass::getConnectedWifiInfor()
{
    loadWifiFromFile();
    return wifi_infor;
}