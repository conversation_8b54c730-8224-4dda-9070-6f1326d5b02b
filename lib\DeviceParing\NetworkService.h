#ifndef NETWORK_H_
#define NETWORK_H_
#include <Arduino.h>
#include "ML_Debug.h"
#define NETWORK_SERVICE_PATH "/network_service.json"

typedef struct WifiInforTypeDefStruct
{
    String ssid = "";
    String password = "";
    IPAddress static_ip;
    IPAddress gateway_ip;
    IPAddress subnet_mask;
} WifiInforTypeDefStruct;

class NetworkServiceClass
{
private:
    WifiInforTypeDefStruct wifi_infor;
    bool need_reinit = false;

public:
    void init();
    void loop();
    void addWiFiEvent();
    bool saveWifiToFile(WifiInforTypeDefStruct *wifi_config_infor);
    bool loadWifiFromFile();
    void connectWifi();
    WifiInforTypeDefStruct getConnectedWifiInfor();
    bool needReinit();
};

extern NetworkServiceClass NetworkService;

#endif