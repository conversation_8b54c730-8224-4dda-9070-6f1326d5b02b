#include "OTAService.h"

WiFiClientSecure client;
APICall ThingsboardOTA;

// Variables to validate
// response from S3
long contentLength = 0;
bool isValidContentType = false;

// Utility to extract header value from headers
String getHeaderValue(String header, String headerName)
{
    return header.substring(strlen(headerName.c_str()));
}

APICall::APICall()
{
}
APICall::~APICall()
{
}

void APICall::setServer(String server, uint16_t port)
{
    _host = server;
    _port = port;
}

void APICall::setDeviceToken(String firmwareToken)
{
    _deviceToken = firmwareToken;
    _firmInfor = "/api/v1/" + _deviceToken + "/attributes";
}

// OTA Logic
void APICall::executeOTA()
{
    Serial.println("Connecting to: " + _host);
    // Connect to S3
    if (client.connect(_host.c_str(), _port))
    {
        // Connection Succeed.
        // Fecthing the _firmwareURI
        Serial.println("Fetching Bin: " + _firmwareURI);

        // Get the contents of the _firmwareURI file
        client.print(String("GET ") + _firmwareURI + " HTTP/1.1\r\n" +
                     "Host: " + _host + "\r\n" +
                     "Cache-Control: no-cache\r\n" +
                     "Connection: close\r\n\r\n");

        // Check what is being sent
        //    DBG(String("GET ") + _firmwareURI + " HTTP/1.1\r\n" +
        //                 "Host: " + _host + "\r\n" +
        //                 "Cache-Control: no-cache\r\n" +
        //                 "Connection: close\r\n\r\n");

        unsigned long timeout = millis();
        while (client.available() == 0)
        {
            if (millis() - timeout > 5000)
            {
                Serial.println("Client Timeout !");
                client.stop();
                return;
            }
        }
        // Once the response is available,
        // check stuff

        /*
           Response Structure
            HTTP/1.1 200 OK
            x-amz-id-2: NVKxnU1aIQMmpGKhSwpCBh8y2JPbak18QLIfE+OiUDOos+7UftZKjtCFqrwsGOZRN5Zee0jpTd0=
            x-amz-request-id: 2D56B47560B764EC
            Date: Wed, 14 Jun 2017 03:33:59 GMT
            Last-Modified: Fri, 02 Jun 2017 14:50:11 GMT
            ETag: "d2afebbaaebc38cd669ce36727152af9"
            Accept-Ranges: bytes
            Content-Type: application/octet-stream
            Content-Length: 357280
            Server: AmazonS3

            {{BIN FILE CONTENTS}}

        */
        while (client.available())
        {
            // read line till /n
            String line = client.readStringUntil('\n');
            // remove space, to check if the line is end of headers
            line.trim();

            // if the the line is empty,
            // this is end of headers
            // break the while and feed the
            // remaining `client` to the
            // Update.writeStream();
            if (!line.length())
            {
                // headers ended
                break; // and get the OTA started
            }

            // Check if the HTTP Response is 200
            // else break and Exit Update
            if (line.startsWith("HTTP/1.1"))
            {
                if (line.indexOf("200") < 0)
                {
                    Serial.println("Got a non 200 status code from server. Exiting OTA Update.");
                    break;
                }
            }

            // extract headers here
            // Start with content length
            if (line.startsWith("Content-Length: "))
            {
                contentLength = atol((getHeaderValue(line, "Content-Length: ")).c_str());
                Serial.println("Got " + String(contentLength) + " bytes from server");
            }

            // Next, the content type
            if (line.startsWith("Content-Type: "))
            {
                String contentType = getHeaderValue(line, "Content-Type: ");
                Serial.println("Got " + contentType + " payload.");
                if (contentType == "application/octet-stream")
                {
                    isValidContentType = true;
                }
            }
        }
    }
    else
    {
        // Connect to S3 failed
        // May be try?
        // Probably a choppy network?
        Serial.println("Connection to " + _host + " failed. Please check your setup");
        // retry??
        // executeOTA();
    }

    // Check what is the contentLength and if content type is `application/octet-stream`
    Serial.println("contentLength : " + String(contentLength) + ", isValidContentType : " + String(isValidContentType));

    // check contentLength and content type
    if (contentLength && isValidContentType)
    {
        // Check if there is enough to OTA Update
        bool canBegin = Update.begin(contentLength);

        // If yes, begin
        if (canBegin)
        {
            Serial.println("Begin OTA. This may take 2 - 5 mins to complete. Things might be quite for a while.. Patience!");
            // No activity would appear on the Serial monitor
            // So be patient. This may take 2 - 5mins to complete
            size_t written = Update.writeStream(client);

            if (written == contentLength)
            {
                Serial.println("Written : " + String(written) + " successfully");
            }
            else
            {
                Serial.println("Written only : " + String(written) + "/" + String(contentLength) + ". Retry?");
                // retry??
                // executeOTA();
            }

            if (Update.end())
            {
                Serial.println("OTA done!");
                if (Update.isFinished())
                {
                    Serial.println("Update successfully completed. Rebooting.");
                    ESP.restart();
                }
                else
                {
                    Serial.println("Update not finished? Something went wrong!");
                }
            }
            else
            {
                Serial.println("Error Occurred. Error #: " + String(Update.getError()));
            }
        }
        else
        {
            // not enough space to begin OTA
            // Understand the partitions and
            // space availability
            Serial.println("Not enough space to begin OTA");
            client.flush();
        }
    }
    else
    {
        Serial.println("There was no content in the response");
        client.flush();
    }
}

OTA_status_check_t APICall::checkFirmwareVersion()
{
    Serial.println("Connecting to: " + _host);
    // Connect to S3
    if (client.connect(_host.c_str(), _port))
    {
        // Connection Succeed.
        // Fecthing the _firmwareURI
        Serial.println("Fetching Bin: " + _firmInfor);

        // Get the contents of the _firmwareURI file
        client.print(String("GET ") + _firmInfor + " HTTP/1.1\r\n" +
                     "Host: " + _host + "\r\n" +
                     "Cache-Control: no-cache\r\n" +
                     "Connection: close\r\n\r\n");

        /* Check what is being sent*/
        Serial.print(String("GET ") + _firmwareURI + " HTTP/1.1\r\n" +
                     "Host: " + _host + "\r\n" +
                     "Cache-Control: no-cache\r\n" +
                     "Connection: close\r\n\r\n");

        unsigned long timeout = millis();
        while (client.available() == 0)
        {
            if (millis() - timeout > 5000)
            {
                Serial.println("Client Timeout !");
                client.stop();
                return ERROR_CHECK_OTA;
            }
        }
        // Once the response is available,
        // check stuff
        while (client.available())
        {
            // read line till /n
            String line = client.readStringUntil('\n');
            // remove space, to check if the line is end of headers
            line.trim();
            if (!line.length())
            {
                // headers ended
                break; // and get the OTA started
            }

            // Check if the HTTP Response is 200
            // else break and Exit Update
            if (line.startsWith("HTTP/1.1"))
            {
                if (line.indexOf("200") < 0)
                {
                    Serial.println("Got a non 200 status code from server. Exiting...");
                    break;
                }
            }
            // extract headers here
            // Start with content length
            if (line.startsWith("Content-Length: "))
            {
                contentLength = atol((getHeaderValue(line, "Content-Length: ")).c_str());
                Serial.println("Got " + String(contentLength) + " bytes from server");
            }
            // Next, the content type
            if (line.startsWith("Content-Type: "))
            {
                String contentType = getHeaderValue(line, "Content-Type: ");
                Serial.println("Got " + contentType + " payload.");
                if (contentType == "application/json")
                {
                    isValidContentType = true;
                }
            }
        }
    }
    else
    {
        Serial.println("Connection to " + _host + " failed. Please check your setup");
    }
    // Check what is the contentLength and if content type is `application/json`
    Serial.println("contentLength : " + String(contentLength) + ", isValidContentType : " + String(isValidContentType));

    String body;
    while (client.available())
    {
        body = client.readStringUntil((char)255);
        // DBG(body);
    }
    // get body done, stop connection
    client.stop();

    /*Parsing body to get firmware infor*/
    DynamicJsonDocument newFirmJson(2048);
    DeserializationError newFirmJsonError = deserializeJson(newFirmJson, body);
    if (newFirmJsonError)
    {
        // DBG(F("deserializeJson() failed: "));
        Serial.println(newFirmJsonError.c_str());
        return ERROR_CHECK_OTA;
    }
    String new_fw_title = newFirmJson["shared"]["fw_title"];
    String new_fw_version = newFirmJson["shared"]["fw_version"];

    /*Compare firmware version*/
    if (new_fw_title == _fw_title && new_fw_version == _fw_version)
    {
        Serial.println("No firmware_available");
        return NO_NEW_FIRM_AVAILABLE;
    }
    else
    {
        Serial.println("New firmware_available, start update OTA");
        /*Store new firmware infor*/
        File file = SPIFFS.open("/firmware_infor.json", FILE_WRITE);
        if (!file)
        {
            Serial.println("Failed to open file /firmware_infor.json ");
            return ERROR_CHECK_OTA;
        }
        if (serializeJson(newFirmJson, file) == 0)
        {
            Serial.println("Failed to write new firmware infor file system");
            return ERROR_CHECK_OTA;
        }
        file.close();
        /*assign new firmware API*/
        _fw_title = new_fw_title;
        _fw_version = new_fw_version;
        _firmwareURI = "/api/v1/" + (String)_deviceToken + "/firmware?&version=" + _fw_version + "&title=" + _fw_title;
        Serial.println(_firmwareURI);
        return NEW_FIRM_AVAILBLE;
    }
}

bool APICall::init()
{

    /*Load old fimrware_infor from File System*/
    File file = SPIFFS.open("/firmware_infor.json", FILE_READ);
    if (!file)
    {
        Serial.println("Failed to open file /firmware_infor.json ");
        return false;
    }

    String data = file.readString();
    DynamicJsonDocument oldFirmJson(2048);
    DeserializationError oldFirmJsonError = deserializeJson(oldFirmJson, data);
    if (oldFirmJsonError)
    {
        Serial.println("Failed to read firmware_infor file system");
        return false;
    }
    file.close();

    String old_fw_title = oldFirmJson["shared"]["fw_title"];
    String old_fw_version = oldFirmJson["shared"]["fw_version"];

    _fw_title = old_fw_title;
    _fw_version = old_fw_version;

    Serial.print("_fw_title: ");
    Serial.println(_fw_title);
    Serial.print("_fw_version: ");
    Serial.println(_fw_version);

    client.setInsecure();
    ThingsboardOTA.setServer(OTA_SERVER, PORT);
    // ThingsboardOTA.setDeviceToken(DEVICE_TOKEN);
}

void APICall::loop()
{
    static uint32_t lastCheckOTA;
    /*Check new firmware: interval 5 minutes*/
    // if (millis() - lastCheckOTA > 5 * 60 * 1000)
    if (millis() - lastCheckOTA > 10*60 * 1000)
    {
        Serial.println("Checking new firmware......");
        OTA_status_check_t otaStatus = ThingsboardOTA.checkFirmwareVersion();
        if (otaStatus == NEW_FIRM_AVAILBLE)
        {
            ThingsboardOTA.executeOTA();
        }
        lastCheckOTA = millis();
    }
}
void readFile(fs::FS &fs, const char *path)
{
    Serial.printf("Reading file: %s\r\n", path);

    File file = fs.open(path);
    if (!file || file.isDirectory())
    {
        Serial.println("− failed to open file for reading");
        return;
    }

    Serial.println("− read from file:");
    while (file.available())
    {
        Serial.write(file.read());
    }
}

void writeFile(fs::FS &fs, const char *path, const char *message)
{
    Serial.printf("Writing file: %s\r\n", path);

    File file = fs.open(path, FILE_WRITE);
    if (!file)
    {
        Serial.println("− failed to open file for writing");
        return;
    }
    if (file.print(message))
    {
        Serial.println("− file written");
    }
    else
    {
        Serial.println("− frite failed");
    }
}

void testUpdateOTA()
{
    client.setInsecure();
    ThingsboardOTA.setServer(OTA_SERVER, PORT);
    ThingsboardOTA.setDeviceToken(DEVICE_TOKEN);

    OTA_status_check_t otaStatus = ThingsboardOTA.checkFirmwareVersion();
    if (otaStatus == NEW_FIRM_AVAILBLE)
    {
        ThingsboardOTA.executeOTA();
    }
}