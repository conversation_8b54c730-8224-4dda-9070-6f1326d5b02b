#ifndef _THINGSBOARD_OTA_H
#define _THINGSBOARD_OTA_H
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <Update.h>
#include <FS.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>

/*DEFINE INFOR FOR OTA SERVER*/
#define OTA_SERVER "iot.viis.tech"
#define PORT 443

// solar-esp-logger device
#define DEVICE_TOKEN "Cmc9iA98HaKkeRO5pizF"

extern WiFiClientSecure client;

typedef enum
{
    NEW_FIRM_AVAILBLE,
    NO_NEW_FIRM_AVAILABLE,
    ERROR_CHECK_OTA
} OTA_status_check_t;

class APICall
{
private:
    String _fw_title;
    String _fw_version;
    String _host;        // Host => bucket-name.s3.region.amazonaws.com
    int _port;           // Non https. For HTTPS 443. As of today, HTTPS doesn't work.
    String _firmwareURI; // _firmwareURI file name with a slash in front.
    String _deviceToken; // device access token of Thingsboard Device
    String _firmInfor;   // GET Firmware infor URI
    /* data */
public:
    APICall(/* args */);
    ~APICall();
    void setServer(String server, uint16_t port);
    void setDeviceToken(String deviceToken);
    OTA_status_check_t checkFirmwareVersion();
    void executeOTA();

    String getFirmwareTitle()
    {
        return _fw_title;
    }
    String getFirmwareVersion()
    {
        return _fw_version;
    }
    String getFirmwareInfor()
    {
        return _firmInfor;
    }

    bool init();
    void loop();
};

extern APICall ThingsboardOTA;

/*Manage File System*/
void readFile(fs::FS &fs, const char *path);
void writeFile(fs::FS &fs, const char *path, const char *message);

void testUpdateOTA();

#endif