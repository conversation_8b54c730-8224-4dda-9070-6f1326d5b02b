#include <Arduino.h>
#include "ParingApiServer.h"
#include <ESPAsyncWebServer.h>
#include "NetworkService.h"
#include "ArduinoJson.h"
#include "DeviceInfor.h"
#include "ThingsboardService.h"

AsyncWebServer server(80);

ParingApiServerClass ParingApiServer;

void ParingApiServerClass::init()
{
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request)
            { request->send(200, "text/plain", "Hi! Device server is working"); });

  server.on("/wifi-infor", HTTP_GET, [](AsyncWebServerRequest *request)
            {
                AsyncResponseStream *response = request->beginResponseStream("application/json");
                WifiInforTypeDefStruct wifi_infor = NetworkService.getConnectedWifiInfor();
                DynamicJsonDocument json(1024);
                json["status"] = "ok";
                json["ssid"] = wifi_infor.ssid;
                json["ip"] = wifi_infor.static_ip.toString();
                json["gateway_ip"] = wifi_infor.gateway_ip.toString();
                serializeJson(json, *response);
                request->send(response); });

  server.on("/device-infor", HTTP_GET, [](AsyncWebServerRequest *request)
            {
                AsyncResponseStream *response = request->beginResponseStream("application/json");
                String serial_number = DeviceInfor.getSerialNumber();
                DynamicJsonDocument json(1024);
                json["status"] = "ok";
                json["serial_number"] = serial_number;
                serializeJson(json, *response);
                request->send(response); });
  server.on(
      "/device-infor", HTTP_POST, [](AsyncWebServerRequest *request) {},
      NULL, [](AsyncWebServerRequest *request, uint8_t *data, size_t len, size_t index, size_t total)
      {
        String jsonString;
        for (size_t i = 0; i < len; i++)
        {
          jsonString += (char)data[i];
          Serial.write(data[i]);
        }
        StaticJsonDocument<512> jsonDoc;
        DeserializationError error = deserializeJson(jsonDoc, jsonString);
        if (error)
        {
          DB_PRINT("Failed to parse JSON: ");
          DB_PRINTLN(error.c_str());
          return;
        }
        const char *serial_number = jsonDoc["serial_number"];
        DeviceInforTypeDefStruct device_infor;
        device_infor.serial_number = String(serial_number);

        char response[512];
        DynamicJsonDocument json(512);
        if (DeviceInfor.saveInforToFile(&device_infor))
        {
          json["status"] = "ok";
        }
        else {
          json["status"] = "fail";
        }
        serializeJson(json, response);
        request->send(404, "application/json", response); });

  server.on("/thingsboard-connection", HTTP_GET, [](AsyncWebServerRequest *request)
            {
                AsyncResponseStream *response = request->beginResponseStream("application/json");
                String serial_number = DeviceInfor.getSerialNumber();
                DynamicJsonDocument json(1024);
                json["status"] = "ok";
                json["tb_connected"] = ThingsboardService.mqttIsConnected();
                serializeJson(json, *response);
                request->send(response); });

  server.on(
      "/thingsboard-credential", HTTP_POST, [](AsyncWebServerRequest *request) {},
      NULL, [](AsyncWebServerRequest *request, uint8_t *data, size_t len, size_t index, size_t total)
      {
        String jsonString;
        for (size_t i = 0; i < len; i++)
        {
          jsonString += (char)data[i];
          Serial.write(data[i]);
        }
        DB_PRINTLN();
        StaticJsonDocument<512> jsonDoc;
        DeserializationError error = deserializeJson(jsonDoc, jsonString);
        if (error)
        {
          DB_PRINT("Failed to parse JSON: ");
          DB_PRINTLN(error.c_str());
          return;
        }
        const char *device_id = jsonDoc["device_id"];
        const char *device_token = jsonDoc["device_token"];
        ThingsboardCredTypeDefStruct tb_credential;
        tb_credential.device_id = String(device_id);
        tb_credential.device_token = String(device_token);

        char response[512];
        DynamicJsonDocument json(512);
        if (ThingsboardService.saveCredentialToFile(&tb_credential))
        {
          json["status"] = "ok";
        }
        else {
          json["status"] = "fail";
        }
        serializeJson(json, response);
        request->send(404, "application/json", response); });

  server.begin();
  DB_PRINTLN("HTTP server started");
}