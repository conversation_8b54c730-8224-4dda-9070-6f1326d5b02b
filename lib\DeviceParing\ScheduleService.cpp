#include "ScheduleService.h"
ScheduleService Schedule;
String ScheduleService::httpGETRequest()
{
    String serverName = "https://iot.viis.tech/api/v2/schedule/device/";
    String payload;
    WiFiClientSecure client;
    client.setInsecure();
    HTTPClient http;

    DynamicJsonDocument doc(8192);

    String serverPath = serverName + (String)ThingsboardServiceClass::getDeviceTokent();

    // Your Domain name with URL path or IP address with path
    http.begin(client, serverPath.c_str());
    DB_PRINTLN("SERVER PATH " + serverPath);

    // Send HTTP GET request
    int httpResponseCode = http.GET();
    if (httpResponseCode > 0)
    {
        DB_PRINTLN("HTTP Response code: ");
        DB_PRINTLN(httpResponseCode);
        payload = http.getString();

        // =========udpate ======
        // Parse the JSON string
        DB_PRINTLN("------------before payload-----------");
        DB_PRINTLN(payload);
        deserializeJson(doc, payload);
        // Remove the "device_id" field from each object in the "data" array
        JsonArray data = doc["result"].as<JsonArray>();
        for (JsonObject obj : data)
        {
            obj.remove("device_id");
        }
        for (JsonObject obj : data)
        {
            obj.remove("name");
        }
        // Convert the modified JSON document back to a string
        String modifiedPayload;
        serializeJson(doc, modifiedPayload);
        // CẦN XOÁ BỚT DATA TRONG DEVICE ID
        DB_PRINTLN("------------after payload-----------");
        DB_PRINTLN(modifiedPayload);
        this->writeFile(SPIFFS, PATH_SCHEDULE, modifiedPayload.c_str());
        // writeFile(SPIFFS, PATH_SCHEDULE, payload.c_str());
    }
    else
    {
        Serial.print("Error code: ");
        DB_PRINTLN(httpResponseCode);
    }
    http.end();
    jsonParseToStruct(payload, _mSchedule);
    return payload;
}
void ScheduleService::jsonParseToStruct(String jsonString, TSchedule *m_schedule)
{
    StaticJsonDocument<4096> doc;
    deserializeJson(doc, jsonString);
    JsonArray array = doc["result"].as<JsonArray>();

    _numOfSchedule = array.size();

    for (int i = 0; i < _numOfSchedule; i++)
    {
        JsonObject actionObject = doc["result"][i]["action"].as<JsonObject>();
        int num_of_object = actionObject.size();
        m_schedule[i].action_count = num_of_object;
        m_schedule[i].enable = doc["result"][i]["enable"].as<bool>();
        m_schedule[i].interval = doc["result"][i]["interval"].as<String>();
        m_schedule[i].time = doc["result"][i]["time"].as<String>();
        int j = 0;
        for (JsonPair kv : actionObject)
        {
            m_schedule[i].action[j].key = kv.key().c_str();
            m_schedule[i].action[j].value = kv.value().as<String>();
            j++;
        }
    }
}

String ScheduleService::readFile(fs::FS &fs, const char *path)
{
    String data = "";
    Serial.printf("Reading file: %s\r\n", path);

    File file = fs.open(path);
    if (!file || file.isDirectory())
    {
        DB_PRINTLN("- failed to open file for reading");
    }

    DB_PRINTLN("- read from file:");
    while (file.available())
    {
        data += (char)file.read();
    }
    file.close();
    return data;
}
void ScheduleService::writeFile(fs::FS &fs, const char *path, const char *message)
{
    Serial.printf("Writing file: %s\r\n", path);

    File file = fs.open(path, FILE_WRITE);
    if (!file)
    {
        DB_PRINTLN("- failed to open file for writing");
        //    createDir(SPIFFS, "/data");
        return;
    }
    if (file.print(message))
    {
        DB_PRINTLN("- file written");
    }
    else
    {
        DB_PRINTLN("- write failed");
    }
    file.close();
}
ScheduleService::ScheduleService(/* args */)
{
    //loadScheduleFromFile();
    // _numOfSchedule = _mSchedule->action_count;
    // DB_PRINTLN("number of sche = "+ (String)_mSchedule->action_count);
}

ScheduleService::~ScheduleService()
{
}

int ScheduleService::getNumberOfSchedule()
{
    //_numOfSchedule = (sizeof(_mSchedule) / sizeof(_mSchedule[0]));
    return _numOfSchedule;
    // return _mSchedule->action_count;
}

TSchedule ScheduleService::getSchedule(int offset)
{
    return _mSchedule[offset];
}

void ScheduleService::loadScheduleFromFile()
{
    this->jsonParseToStruct(this->readFile(SPIFFS, PATH_SCHEDULE), _mSchedule);
}
