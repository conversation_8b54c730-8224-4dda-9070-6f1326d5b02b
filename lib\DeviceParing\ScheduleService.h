#ifndef _SCHEDULE_SERVICE_H_
#define _SCHEDULE_SERVICE_H_

#include "Arduino.h"
#include "ArduinoJson.h"
#include <WiFiClient.h>
#include <HTTPClient.h>
#include <ThingsboardService.h>
#include <FS.h>
#include <SPIFFS.h>
#define MAX_ACTION 21
#define MAX_TIMER 21
#define PATH_SCHEDULE "/schedule.json"
typedef struct
{
    String key;
    String value;
} Action;

typedef struct
{
    bool enable;
    String time;
    String interval;
    int action_count;
    Action action[MAX_ACTION];
} TSchedule;
class ScheduleService : public ThingsboardServiceClass
{
private:
    int _numOfSchedule = 0;

    String readFile(fs::FS &fs, const char *path);
    void writeFile(fs::FS &fs, const char *path, const char *message);

public:
    TSchedule _mSchedule[MAX_TIMER];
    void jsonParseToStruct(String jsonString, TSchedule *m_schedule);
    ScheduleService(/* args */);
    ~ScheduleService();
    int getNumberOfSchedule();
    TSchedule getSchedule(int offset);
    void loadScheduleFromFile();
    String httpGETRequest();
};
extern ScheduleService Schedule;

#endif