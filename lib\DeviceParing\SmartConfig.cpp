#include <Arduino.h>
#include "SmartConfig.h"
#include "WiFi.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"
SmartConfigClass SmartConfig;

void SmartConfigClass::init()
{
    pinMode(SMART_CONF_BUTTON, INPUT);
    pinMode(SMART_CONF_LED, OUTPUT);
}

void SmartConfigClass::ledOn()
{
    digitalWrite(SMART_CONF_LED, HIGH);
}
void SmartConfigClass::ledOff()
{
    digitalWrite(SMART_CONF_LED, LOW);
}
bool SmartConfigClass::btnIsPressed()
{
   
    // return digitalRead(SMART_CONF_BUTTON);
    return !digitalRead(SMART_CONF_BUTTON);
}

void SmartConfigClass::loop()
{
    switch (paring_state)
    {
    case READY:
        if (millis() - last_check_button > INTERVAL_CHECK_BUTTON_MS)
        {
            switch (btn_curr_state)
            {
            case IS_RELEASING:
                if (btnIsPressed())
                {
                    DB_PRINTLN("First time press");
                    btn_curr_state = IS_PRESSING;
                }
                else
                {
                    last_btn_is_released = millis();
                }
                /* code */
                break;

            case IS_PRESSING:
                if (btnIsPressed())
                {
                    DB_PRINTLN("After time press");
                    if (millis() - last_btn_is_released > BUTTON_ENTER_SMART_CONF_TIME_MS)
                    {
                        paring_state = SMART_CONF_RUNNING;
                    }
                }
                else
                {
                    btn_curr_state = IS_RELEASING;
                    last_btn_is_released = millis();
                }
                break;

            default:
                break;
            }
            last_check_button = millis();
        }
        break;

    case SMART_CONF_RUNNING:
    {
        WiFi.mode(WIFI_AP_STA);
        /* start SmartConfig */
        WiFi.beginSmartConfig();

        /* Wait for SmartConfig packet from mobile */
        DB_PRINTLN("Waiting for SmartConfig.");
        static bool toggle_status = true;
        while (!WiFi.smartConfigDone())
        {
            if (toggle_status)
            {
                ledOn();
            }
            else
            {
                ledOff();
            }
            toggle_status = !toggle_status;

            delay(500);
            DB_PRINT(".");
        }
        DB_PRINTLN("");
        DB_PRINTLN("SmartConfig done.");

        /* Wait for WiFi to connect to AP */
        DB_PRINTLN("Waiting for WiFi");
        while (WiFi.status() != WL_CONNECTED)
        {
            if (toggle_status)
            {
                ledOn();
            }
            else
            {
                ledOff();
            }
            toggle_status = !toggle_status;
            delay(500);
            DB_PRINT(".");
        }
        DB_PRINTLN("WiFi Connected.");
        DB_PRINTLN("ssid = ");
        DB_PRINTLN(WiFi.SSID());
        DB_PRINTLN("password = ");
        DB_PRINTLN(WiFi.psk());
        DB_PRINT("IP Address: ");
        DB_PRINTLN(WiFi.localIP());
        DB_PRINT("IP Gateway: ");
        DB_PRINTLN(WiFi.gatewayIP());
        DB_PRINT("Subnet: ");
        DB_PRINTLN(WiFi.subnetMask());

        WifiInforTypeDefStruct wifiInfor;
        wifiInfor.ssid = WiFi.SSID();
        wifiInfor.password = WiFi.psk();
        wifiInfor.static_ip = WiFi.localIP();
        wifiInfor.gateway_ip = WiFi.gatewayIP();
        wifiInfor.subnet_mask = WiFi.subnetMask();
        NetworkService.saveWifiToFile(&wifiInfor);

        paring_state = SMART_CONF_DONE;
        break;
    }

    case SMART_CONF_DONE:
        last_btn_is_released = millis();
        btn_curr_state = IS_RELEASING;
        paring_state = READY;
        break;

    default:
        break;
    }
}
