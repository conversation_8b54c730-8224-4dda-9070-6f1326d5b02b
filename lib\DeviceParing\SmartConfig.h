
#ifndef DEVICE_PARING_H_
#define DEVICE_PARING_H_

#include "NetworkService.h"
#include "ML_Debug.h"

#ifndef SMART_CONF_BUTTON
#define SMART_CONF_BUTTON 0
#endif

#ifndef SMART_CONF_LED
#define SMART_CONF_LED 5
#endif

#define INTERVAL_CHECK_BUTTON_MS 100
#define BUTTON_ENTER_SMART_CONF_TIME_MS 3000

typedef enum ButtonStateTypedefEnum
{
    IS_RELEASING,
    IS_PRESSING
} ButtonStateTypedefEnum;

typedef enum ParingStateTypedefEnum
{
    READY = 0,
    SMART_CONF_RUNNING,
    SMART_CONF_DONE
} ParingStateTypedefEnum;

class SmartConfigClass
{
private:
    ButtonStateTypedefEnum btn_curr_state = IS_RELEASING;
    bool btnIsPressed();
    uint32_t last_btn_is_released = millis();
    uint32_t last_check_button = millis();
    ParingStateTypedefEnum paring_state = READY;
    void ledOn();
    void ledOff();

public:
    void init();
    void loop();
};

extern SmartConfigClass SmartConfig;

#endif