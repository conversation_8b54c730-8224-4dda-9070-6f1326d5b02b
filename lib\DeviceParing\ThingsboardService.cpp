#include "Arduino.h"
#include "ThingsboardService.h"
#include "ArduinoJson.h"
#include "SPIFFS.h"
#include "PubSubClient.h"
#include "WiFiClient.h"

WiFiClient wifiClient;
PubSubClient mqttClient(wifiClient);

const char *mqtt_server = "mqtt.viis.tech";
const char *topic = "v1/devices/me/telemetry";

ThingsboardServiceClass ThingsboardService;

void ThingsboardServiceClass::init(MQTT_CALLBACK_SIGNATURE)
{
    this->loadCredentialFromFile();
    mqttClient.setServer(mqtt_server, 1883);
    mqttClient.setCallback(callback);
}
void ThingsboardServiceClass::loop()
{
    mqttClient.loop();
    if (!mqttClient.connected())
    {
        static uint32_t lastReconnectMqtt = millis();

        if (millis() - lastReconnectMqtt > 10000)
        {
            DB_PRINTLN("mqtt not connected ");
            DB_PRINTLN("Reconnect Thingsboard MQTT");
            this->connectMqtt();
            lastReconnectMqtt = millis();
        }
    }
}

void ThingsboardServiceClass::connectMqtt()
{
    // Loop until we're mqttReconnected
    if (!mqttClient.connected())
    {
        DB_PRINTLN("Attempting MQTT connection...");
        // Attempt to connect
        if (mqttClient.connect(
                this->credential.device_id.c_str(), this->credential.device_token.c_str(), ""))
        {
            DB_PRINTLN("connected");
            eventConnected = true;
            // Once connected, publish an announcement...
            mqttClient.subscribe("v1/devices/me/rpc/request/+");
            mqttClient.subscribe("v1/devices/me/attributes");
        }
        else
        {
            DB_PRINT("failed, rc=");
            DB_PRINT(mqttClient.state());
            DB_PRINTLN(" try again in 30 seconds");
            // Wait 5 seconds before retrying
        }
    }
    need_reconnect = false;
}

bool ThingsboardServiceClass::mqttIsConnected()
{
    return mqttClient.connected();
}

bool ThingsboardServiceClass::saveCredentialToFile(ThingsboardCredTypeDefStruct *tbCredential)
{
    StaticJsonDocument<1024> doc;
    doc["device_id"] = tbCredential->device_id;
    doc["device_token"] = tbCredential->device_token;
    /* Save config to main file */
    DB_PRINTLN("Save config to file THINGSBOARD_CREDENTIAL_PATH");

    File file = SPIFFS.open(THINGSBOARD_CREDENTIAL_PATH, FILE_WRITE);
    if (!file)
    {
        DB_PRINTLN("Failed to open file THINGSBOARD_CREDENTIAL_PATH");
        file.close();
        return false;
    }

    if (serializeJson(doc, file) == 0)
    {
        DB_PRINTLN("Failed to write to file THINGSBOARD_CREDENTIAL_PATH");
        file.close();
        return false;
    }

    file.close();
    DB_PRINTLN("Success save config to file THINGSBOARD_CREDENTIAL_PATH");

    this->credential.device_id = tbCredential->device_id;
    this->credential.device_token = tbCredential->device_token;
    need_reconnect = true;
    return true;
}

bool ThingsboardServiceClass::loadCredentialFromFile()
{
    StaticJsonDocument<512> doc;

    DB_PRINTLN("Read network config THINGSBOARD_CREDENTIAL_PATH ");
    File file = SPIFFS.open(THINGSBOARD_CREDENTIAL_PATH, FILE_READ);
    if (!file)
    {
        DB_PRINTLN("Failed to open file THINGSBOARD_CREDENTIAL_PATH");
        return false;
    }
    DeserializationError error = deserializeJson(doc, file);
    if (error)
    {
        DB_PRINTLN("Failed to deserializeJson file");
        return false;
    }
    file.close();
    const char *tb_device_id = doc["device_id"];
    const char *tb_device_token = doc["device_token"];
    deviceToken = tb_device_token;
    this->credential.device_id = String(tb_device_id);
    this->credential.device_token = String(tb_device_token);
    DB_PRINTLN("Success Read wifi infor to THINGSBOARD_CREDENTIAL_PATH");
    return true;
}
void ThingsboardServiceClass::sendTelemertry(String key, bool value)
{
    DynamicJsonDocument data(200);
    value == true ? data[key] = true : data[key] = false;
    String objectString;
    serializeJson(data, objectString);
    mqttClient.publish("v1/devices/me/telemetry", objectString.c_str());
    DB_PRINTLN("da gui");
    DB_PRINTLN(objectString);
}
void ThingsboardServiceClass::sendTelemertry(String key, float value)
{
    DynamicJsonDocument data(200);
    data[key] = value;
    String objectString;
    serializeJson(data, objectString);
    mqttClient.publish(topic, objectString.c_str());
    DB_PRINTLN(objectString);
}
void ThingsboardServiceClass::sendTelemertry(String key, String value)
{
    DynamicJsonDocument data(200);
    data[key] = value;
    String objectString;
    serializeJson(data, objectString);
    mqttClient.publish(topic, objectString.c_str());
    DB_PRINTLN(objectString);
}
void ThingsboardServiceClass::sendTelemertry(String key, int value)
{
    DynamicJsonDocument data(200);
    data[key] = value;
    String objectString;
    serializeJson(data, objectString);
    mqttClient.publish(topic, objectString.c_str());
    DB_PRINTLN(objectString);
}
void ThingsboardServiceClass::sendTelemertry(String key, IPAddress value)
{
    DynamicJsonDocument data(200);
    data[key] = value.toString();
    String objectString;
    serializeJson(data, objectString);
    mqttClient.publish(topic, objectString.c_str());
    DB_PRINTLN(objectString);
}
void ThingsboardServiceClass::sendTelemertry(String jsonString)
{
    mqttClient.publish(topic, jsonString.c_str());
    DB_PRINTLN(jsonString);
}
void ThingsboardServiceClass::setFirstJsonData(String jsonData)
{
    firstJsonData = jsonData;
}
String ThingsboardServiceClass::getDeviceID()
{
    return this->credential.device_id;
}
String ThingsboardServiceClass::getDeviceTokent()
{
    this->loadCredentialFromFile();
    return this->credential.device_token;
}
bool ThingsboardServiceClass::getEventConnected()
{
    return eventConnected;
}
void ThingsboardServiceClass::removeEventConncted()
{
    eventConnected = false;
}
void ThingsboardServiceClass::sendTelemertry(String key, double value)
{
    DynamicJsonDocument data(200);
    data[key] = value;
    String objectString;
    serializeJson(data, objectString);
    mqttClient.publish(topic, objectString.c_str());
    DB_PRINTLN(objectString);
}
void ThingsboardServiceClass::sendFirstJsonData()
{
    sendTelemertry(firstJsonData);
}