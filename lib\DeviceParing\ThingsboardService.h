#ifndef THINGSBOARD_SERVICE_H_
#define THINGSBOARD_SERVICE_H_

#include <Arduino.h>
#include "ML_Debug.h"
#if defined(ESP8266) || defined(ESP32)
#include <functional>
#define MQTT_CALLBACK_SIGNATURE std::function<void(char *, uint8_t *, unsigned int)> callback
#else
#define MQTT_CALLBACK_SIGNATURE void (*callback)(char *, uint8_t *, unsigned int)
#endif

// #define DEBUG
// #ifdef DEBUG
// #define DB_PRINTLN(x) Serial.println(x)
// #define DB_PRINT(x) Serial.print(x)
// #else
// #define DB_PRINTLN(x)
// #define DB_PRINT(x)
// #endif

#define THINGSBOARD_CREDENTIAL_PATH "/thingsboard_credential.json"

typedef struct ThingsboardCredTypeDefStruct
{
    String device_id = "";
    String device_token = "";
} ThingsboardCredTypeDefStruct;

class ThingsboardServiceClass
{
private:
    ThingsboardCredTypeDefStruct credential;
    bool need_reconnect = false;
    String deviceToken = "";
    String firstJsonData = "";
    void sendFirstJsonData();
    bool eventConnected=false;

public:
    void init(MQTT_CALLBACK_SIGNATURE);
    void loop();
    bool needReconnect() { return need_reconnect; }
    void connectMqtt();
    bool mqttIsConnected();
    bool loadCredentialFromFile();
    bool saveCredentialToFile(ThingsboardCredTypeDefStruct *tbCredential);

    void sendTelemertry(String key, bool value);
    void sendTelemertry(String key, float value);
    void sendTelemertry(String key, int value);
    void sendTelemertry(String key, double value);
    void sendTelemertry(String key, String value);
    void sendTelemertry(String key, IPAddress value);
    void sendTelemertry(String jsonString);
    void setFirstJsonData(String jsonData);
    String getDeviceID();
    String getDeviceTokent();
    bool getEventConnected();
    void removeEventConncted();
};

extern ThingsboardServiceClass ThingsboardService;

#endif