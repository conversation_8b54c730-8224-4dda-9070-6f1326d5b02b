# Arduino Library for the ESP32 CAN Bus (ESP32-Arduino-CAN)

## Features

* Support the CAN Bus from the ESP32 (SJA1000)
* CAN Messages send and receive 
* Various Bus speeds
* Standard and Extended Frames
* CAN Message Filter



## Third Party Components

- Arduino-ESP32-CAN-Demo
  - Arduino CAN Demo from [iotsharing.com - nhatuan84](https://github.com/nhatuan84/arduino-esp32-can-demo)

- ESPCan Driver 
  - Base CAN Driver from [<PERSON>](https://github.com/ThomasBarth/ESP32-CAN-Driver) and [Nayar Systems](https://github.com/nayarsystems/ESP32-CAN-Driver)
  - General [Component CAN Driver Pack](https://github.com/ESP32DE/ESP32-CAN-Driver/tree/Component_CAN_Driver_Pack) Work for ESP-IDF with menuconfig from [rudi ;-)](http://esp32.de)

## Usage

See the examples in the [/examples](examples) folder.