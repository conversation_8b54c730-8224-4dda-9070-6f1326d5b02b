#include "Hangyong_vx.h"

void Hangyong_vx::begin(Stream *port, uint8_t ID)
{
    ML_ModbusRtuMaster::begin(port);
    slave_id=ID;
}

void Hangyong_vx::readData()
{
    uint16_t data_rev[5]={0};
    ML_ModbusRtuMaster::readHoldingRegisters(slave_id,1,5,data_rev);
    current_temperature=data_rev[0];
    current_set_temperature=data_rev[1];
}

uint16_t Hangyong_vx::getCurrentSetTemperature()
{
    return current_set_temperature;
}

uint16_t Hangyong_vx::getCurrentTemperature()
{
    return current_temperature;
}

Hangyong_vx::Hangyong_vx(/* args */)
{
}

Hangyong_vx::~Hangyong_vx()
{
}