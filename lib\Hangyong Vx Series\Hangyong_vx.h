#ifndef HANGYONG_VX_H_
#define HANGYONG_VX_H_
#include "Arduino.h"
#include "ML_ModbusRtuMaster.h"




class Hangyong_vx : public ML_ModbusRtuMaster
{
private:
    Stream *m_port;
    uint16_t current_temperature=0;
    uint16_t current_set_temperature=0;
    u_int8_t slave_id =1;
    /* data */
public:
    Hangyong_vx(/* args */);
    ~Hangyong_vx();
void begin(Stream *port, uint8_t ID);
void readData();
uint16_t getCurrentTemperature();
uint16_t getCurrentSetTemperature();

};



#endif