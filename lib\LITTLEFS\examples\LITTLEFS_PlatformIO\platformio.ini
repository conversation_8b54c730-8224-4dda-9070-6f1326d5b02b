; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
default_envs = esp32

[env]
framework = arduino

[env:esp32]
platform = espressif32
;platform = https://github.com/platformio/platform-espressif32.git
;board_build.mcu = esp32
platform_packages = framework-arduinoespressif32 @ https://github.com/espressif/arduino-esp32.git

build_flags = 
	${env.build_flags} 
	-D=${PIOENV} 
	;-D CONFIG_LITTLEFS_FOR_IDF_3_2

lib_deps = https://github.com/lorol/LITTLEFS.git

board = esp32dev
;board_build.partitions = partitions_custom.csv
monitor_filters = esp32_exception_decoder
monitor_speed = 115200

extra_scripts = ./littlefsbuilder.py
