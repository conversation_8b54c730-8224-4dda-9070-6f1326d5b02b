###########################################
# Syntax Coloring Map For LiquidCrystal_I2C
###########################################

###########################################
# Datatypes (KEYWORD1)
###########################################

LiquidCrystal_I2C	KEYWORD1

###########################################
# Methods and Functions (KEYWORD2)
###########################################
init	KEYWORD2
begin	KEYWORD2
clear	KEYWORD2
home	KEYWORD2
noDisplay	KEYWORD2
display	KEYWORD2
noBlink	KEYWORD2
blink	KEYWORD2
noCursor	KEYWORD2
cursor	KEYWORD2
scrollDisplayLeft	KEYWORD2
scrollDisplayRight	KEYWORD2
leftToRight	KEYWORD2
rightToLeft	KEYWORD2
shiftIncrement	KEYWORD2
shiftDecrement	KEYWORD2
noBacklight	KEYWORD2
backlight	KEYWORD2
autoscroll	KEYWORD2
noAutoscroll	KEYWORD2
createChar	KEYWORD2
setCursor	KEYWORD2
print	KEYWORD2
blink_on	KEYWORD2
blink_off	KEYWORD2
cursor_on	KEYWORD2
cursor_off	KEYWORD2
setBacklight	KEYWORD2
load_custom_character	KEYWORD2
printstr	KEYWORD2
###########################################
# Constants (LITERAL1)
###########################################
