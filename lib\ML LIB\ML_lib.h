#ifndef  ML_LIB_H_
#define ML_LIB_H_

float mapfloat(long x, long in_min, long in_max, long out_min, long out_max)
{
    return (float)(x - in_min) * (out_max - out_min) / (float)(in_max - in_min) + out_min;
}
float mapFloatToRange(float input, float fromLow, float fromHigh, float toLow, float toHigh) {
  // Chuyển đổi giá trị float từ khoảng [fromLow, fromHigh] thành khoảng [toLow, toHigh]
  return (input - fromLow) * (toHigh - toLow) / (fromHigh - fromLow) + toLow;
}
uint32_t bigEndianABCD(uint16_t AB, uint16_t CD)
{
    return static_cast<uint32_t>(AB) << 16 | static_cast<uint32_t>(CD);
}
#endif