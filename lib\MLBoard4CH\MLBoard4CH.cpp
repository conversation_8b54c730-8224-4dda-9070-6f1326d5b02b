#include "MLBoard4CH.h"

void MLBoard4CH::begin()
{
    pinMode(I1, INPUT);
    pinMode(I2, INPUT);
    pinMode(I3, INPUT);
    pinMode(I4, INPUT);

    pinMode(Q1, OUTPUT);
    pinMode(Q2, OUTPUT);
    pinMode(Q3, OUTPUT);
    pinMode(Q4, OUTPUT);
}

void MLBoard4CH::testInput()
{
    for (int i = 0; i < 4; i++)
    {
        digitalWrite(output[i], digitalRead(input[i]));
    }
}

void MLBoard4CH::testOutput()
{
    for (int i = 0; i < 4; i++)
    {
        digitalWrite(output[i], !digitalRead(output[i]));
    }
}

void MLBoard4CH::testRelay()
{
    for(int i=0;i<4;i++){
        digitalWrite(output[i],!digitalRead(output[i]));
    }
}

MLBoard4CH::MLBoard4CH(/* args */)
{
}

MLBoard4CH::~MLBoard4CH()
{
}
