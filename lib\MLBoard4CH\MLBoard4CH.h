#ifndef _ML_BOARD_4CH_H_
#define _ML_BOARD_4CH_H_
#include "Arduino.h"

#define I1 34
#define I2 35
#define I3 32
#define I4 33

#define Q1 2
#define Q2 4
#define Q3 16
#define Q4 5

#define I2C_SCL 22
#define I2C_SA 21

#define UART_TI 17
#define UART_RI 16

const uint8_t input[4] = {34, 35, 32, 33};
const uint8_t output[4] = {2, 4, 16, 5}; // 4io

class MLBoard4CH
{
public:
    void begin();
    void testInput();
    void testOutput();
    void testRelay();
    MLBoard4CH(/* args */);
    ~MLBoard4CH();
};

#endif
