#ifndef _ML_BOARD_8CH_H_
#define _ML_BOARD_8CH_H_
#include "Arduino.h"
#include "RTClib.h"

#define I1 18
#define I2 19
#define I3 34
#define I4 35
#define I5 23
#define I6 25
#define I7 26
#define I8 27

#define Q1 2
#define Q2 4
#define Q3 5
#define Q4 12
#define Q5 13
#define Q6 14
#define Q7 15
#define Q8 32

#define I2C_SCL 22
#define I2C_SA 21

#define UART_TI 17
#define UART_RI 16       
const uint8_t input[8] = {35, 34, 19, 18, 27, 26, 25, 23};
const uint8_t output[8] = {2, 4, 5, 12, 13, 14, 15, 32};

class MLBoard8CH
{
private:
    RTC_DS1307 *rtc;

public:
    void begin();
    void testInput();
    void testOutput();
    void testRelay();
    void testRTCPrint();
    void adjustTime(unsigned long time);
    int getDayOfWeek();
    String getStringDayOfWeek();
    String getTimeString();
    MLBoard8CH(/* args */);
    ~MLBoard8CH();
};

#endif