#include <LocalTime.h>

LocalTime::LocalTime(ModbusRTU *_rtu,int _id)
{
    _rtu = rtu;
    id=_id;
}
void LocalTime::setTimeOverRtu()
{

    rtu->writeHreg(id,0,setTimeBuff,8);

}
/**
 *  According to the available array, set all the time
 *  setTimeBuff Array in the following format (type is uint16_t)
 *    setTimeBuff[0] for eSEC type, range: 00-59
 *    setTimeBuff[1] for eMIN type, range: 00-59
 *    setTimeBuff[2] for eHR type, range: 00-23
 *    setTimeBuff[3] for eDOW type, range: 01-07
 *    setTimeBuff[4] for eDATE type, range: 01-31
 *    setTimeBuff[5] for eMTH type, range: 01-12
 *    setTimeBuff[6] for eYR type, range: 2000-2099
 *  Note: Values out of range will result in a setting error
 */
String LocalTime::getTimeOverRtu()
{
    String time;
    uint16_t getTimeBuff[7];
    char outputarr[128];
    rtu->readHreg(id, 8, getTimeBuff, 7);
    sprintf(outputarr, "time: %d/%d/%d-%d %d:%d:%d\r\n",
            getTimeBuff[6],
            getTimeBuff[5],
            getTimeBuff[4],
            getTimeBuff[3],
            getTimeBuff[2],
            getTimeBuff[1],
            getTimeBuff[0]);
    Serial.print(outputarr);
    time = outputarr;
    return String();
}

LocalTime::~LocalTime()
{
}
