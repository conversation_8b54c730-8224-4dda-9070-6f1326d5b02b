#include "MLDwinRotary.h"

void MLDwinRotary::updateDisplayPageDwin()
{
	if (dwin->getIndexPage() != rotary->readEncoder())
	{
		dwin->dwinShowPage(rotary->readEncoder());
	}
}
void MLDwinRotary::updateStateMenu()
{
	switch (STATE_MENU_SETTING)
	{
	case HOME_NEMU:
	{
		updateDisplayPageDwin();
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_MENU_SETTING = static_cast<MLDwinRotary::SETING>(rotary->readEncoder());
			if (STATE_MENU_SETTING == SETTING_LUU_LUONG)
			{
				STATE_SETTING_LL = HOME_SETTING_LL;
				rotary->setEncoderValue(SETTING_MODE);
				rotary->setBoundaries(SETTING_MODE, EXIT_LL);
			}
			else if (STATE_MENU_SETTING == SETTING_THOI_GIAN_TUOI)
			{
				STATE_SETTING_TGT = HOME_SETTING_TGT;
				rotary->setEncoderValue(SET_TGT_NUOC);
				rotary->setBoundaries(SET_TGT_NUOC, EXIT_TGT);
			}
			else if (STATE_MENU_SETTING == SETTING_THOI_GIAN)
			{
				STATE_SETTING_TIME = HOME_SETTING_TIME;
				rotary->setEncoderValue(SET_GIO);
				rotary->setBoundaries(SET_GIO, EXIT_SET_TIME);
			}
			else if (STATE_MENU_SETTING == THONG_TIN_HEN_GIO)
			{
				STATE_TT_HEN_GIO = HOME_TT_HEN_GIO;
				rotary->setEncoderValue(HOME_TT_HEN_GIO);
				rotary->setBoundaries(HOME_TT_HEN_GIO, EXIT_TT_HEN_GIO);
			}
		}
		break;
	}
	case SETTING_LUU_LUONG:
	{
		updateStateSettingLuuLuong();
		break;
	}
	case SETTING_THOI_GIAN_TUOI:
	{
		updateStateSettingThoiGianTuoi();
		break;
	}
	case SETTING_THOI_GIAN:
	{
		updateStateSettingThoiGian();
		break;
	}
	case THONG_TIN_HEN_GIO:
	{
		updateStateThongTinHenGio();
		break;
	}
	case EXIT_MENU:
	{
		DW_PAGE = HOME_PAGE;
		break;
	}
	}
}
void MLDwinRotary::updateStateSettingLuuLuong()
{
	switch (STATE_SETTING_LL)
	{
	case HOME_SETTING_LL:
	{
		updateDisplayPageDwin();
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_LL = static_cast<MLDwinRotary::ST_LUU_LUONG>(rotary->readEncoder());
			if (STATE_SETTING_LL == SETTING_MODE)
			{
				rotary->setBoundaries(0, 1);
				rotary->setEncoderValue(0);
			}
			else
			{
				Serial.println("data: " + (String)dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A]);
				rotary->setBoundaries(0, MAX_LUU_LUONG);
				rotary->setEncoderValue(dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A]);
			}
		}
		break;
	}
	case SETTING_MODE:
	{
		if (rotary->readEncoder() > 0)
		{
			dwinSetData.mode = "AUTO";
		}
		else
		{
			dwinSetData.mode = "MANUAL";
		}
		dwin->writeVP(MODE_ADDR, dwinSetData.mode + "                        ");
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_LL = HOME_SETTING_LL;
			rotary->setBoundaries(SETTING_MODE, EXIT_LL);
			rotary->setEncoderValue(SET_LL_A);
		}
		break;
	}
	case SET_LL_A:
	{
		dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A] = rotary->readEncoder();
		dwin->writeVP(SET_FLOW_A_ADDR, dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_LL = HOME_SETTING_LL;
			rotary->setEncoderValue(SET_LL_B);
			rotary->setBoundaries(SETTING_MODE, EXIT_LL);
		}
		break;
	}
	case SET_LL_B:
	{
		dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A] = rotary->readEncoder();
		dwin->writeVP(SET_FLOW_B_ADDR, dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A]);
		Serial.println("SET_LL_B");
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_LL = HOME_SETTING_LL;
			rotary->setEncoderValue(SET_LL_C);
			rotary->setBoundaries(SETTING_MODE, EXIT_LL);
		}
		break;
	}
	case SET_LL_C:
	{

		dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A] = rotary->readEncoder();
		dwin->writeVP(SET_FLOW_C_ADDR, dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_LL = HOME_SETTING_LL;
			rotary->setEncoderValue(SET_LL_D);
			rotary->setBoundaries(SETTING_MODE, EXIT_LL);
		}
		break;
	}
	case SET_LL_D:
	{
		dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A] = rotary->readEncoder();
		dwin->writeVP(SET_FLOW_D_ADDR, dwinSetData.set_luu_luong[STATE_SETTING_LL - SET_LL_A]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_LL = HOME_SETTING_LL;
			rotary->setEncoderValue(EXIT_LL);
			rotary->setBoundaries(SETTING_MODE, EXIT_LL);
		}
		break;
	}
	case EXIT_LL:
	{
		STATE_MENU_SETTING = HOME_NEMU;
		rotary->setBoundaries(SETTING_LUU_LUONG, EXIT_MENU);
		rotary->setEncoderValue(SETTING_LUU_LUONG);
		break;
	}
	}
}

void MLDwinRotary::updateStateSettingThoiGianTuoi()
{
	switch (STATE_SETTING_TGT)
	{
	case HOME_SETTING_TGT:
	{
		updateDisplayPageDwin();
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TGT = static_cast<MLDwinRotary::ST_THOI_GIAN_TUOI>(rotary->readEncoder());
			rotary->setBoundaries(0, MAX_TIME_SET);
			rotary->setEncoderValue(dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC]);
		}
		break;
	}
	case SET_TGT_NUOC:
	{
		dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC] = rotary->readEncoder();
		dwin->writeVP(SET_TIME_FRESH_WATER_ADDR, dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TGT = HOME_SETTING_TGT;
			rotary->setBoundaries(SET_TGT_NUOC, EXIT_TGT);
			rotary->setEncoderValue(SET_TGT_A);
		}
		break;
	}
	case SET_TGT_A:
	{
		dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC] = rotary->readEncoder();
		dwin->writeVP(SET_TIME_CH_A_ADDR, dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TGT = HOME_SETTING_TGT;
			rotary->setBoundaries(SET_TGT_NUOC, EXIT_TGT);
			rotary->setEncoderValue(SET_TGT_B);
		}
		break;
	}
	case SET_TGT_B:
	{
		dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC] = rotary->readEncoder();
		dwin->writeVP(SET_TIME_CH_B_ADDR, dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TGT = HOME_SETTING_TGT;
			rotary->setBoundaries(SET_TGT_NUOC, EXIT_TGT);
			rotary->setEncoderValue(SET_TGT_C);
		}
		break;
	}
	case SET_TGT_C:
	{
		dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC] = rotary->readEncoder();
		dwin->writeVP(SET_TIME_CH_C_ADDR, dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TGT = HOME_SETTING_TGT;
			rotary->setBoundaries(SET_TGT_NUOC, EXIT_TGT);
			rotary->setEncoderValue(SET_TGT_D);
		}
		break;
	}
	case SET_TGT_D:
	{
		dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC] = rotary->readEncoder();
		dwin->writeVP(SET_TIME_CH_D_ADDR, dwinSetData.set_thoi_gian_tuoi[STATE_SETTING_TGT - SET_TGT_NUOC]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TGT = HOME_SETTING_TGT;
			rotary->setBoundaries(SET_TGT_NUOC, EXIT_TGT);
			rotary->setEncoderValue(EXIT_TGT);
		}
		break;
	}
	case EXIT_TGT:
	{
		STATE_MENU_SETTING = HOME_NEMU;
		dwin->setBuzzer(0x000A);
		rotary->setBoundaries(SETTING_LUU_LUONG, EXIT_MENU);
		rotary->setEncoderValue(SETTING_THOI_GIAN_TUOI);
		break;
	}
	}
}
void MLDwinRotary::updateStateSettingThoiGian()
{
	switch (STATE_SETTING_TIME)
	{
	case HOME_SETTING_TIME:
	{
		updateDisplayPageDwin();
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TIME = static_cast<MLDwinRotary::ST_THOI_GIAN>(rotary->readEncoder());
			rotary->setBoundaries(0, MAX_SETTING_TIME_VALUE[STATE_SETTING_TIME - SET_GIO]);
			rotary->setEncoderValue(dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO]);
		}
		break;
	}
	case SET_GIO:
	{
		Serial.println("set giowf");
		dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO] = rotary->readEncoder();
		dwin->writeVP(SET_HH_ADDR, dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TIME = HOME_SETTING_TIME;
			rotary->setBoundaries(SET_GIO, EXIT_SET_TIME);
			rotary->setEncoderValue(SET_PHUT);
		}
		break;
	}
	case SET_PHUT:
	{
		dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO] = rotary->readEncoder();
		dwin->writeVP(SET_MM_ADDR, dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TIME = HOME_SETTING_TIME;
			rotary->setBoundaries(SET_GIO, EXIT_SET_TIME);
			rotary->setEncoderValue(SET_NGAY);
		}
		break;
	}
	case SET_NGAY:
	{
		dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO] = rotary->readEncoder();
		dwin->writeVP(SET_DDD_ADDR, dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TIME = HOME_SETTING_TIME;
			rotary->setBoundaries(SET_GIO, EXIT_SET_TIME);
			rotary->setEncoderValue(SET_THANG);
		}
		break;
	}
	case SET_THANG:
	{
		dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO] = rotary->readEncoder();
		dwin->writeVP(SET_MMM_ADDR, dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TIME = HOME_SETTING_TIME;
			rotary->setBoundaries(SET_GIO, EXIT_SET_TIME);
			rotary->setEncoderValue(SET_NAM);
		}
		break;
	}
	case SET_NAM:
	{
		dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO] = rotary->readEncoder();
		dwin->writeVP(SET_YYY_ADDR, dwinSetData.set_time_rtc[STATE_SETTING_TIME - SET_GIO]);
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_SETTING_TIME = HOME_SETTING_TIME;
			rotary->setBoundaries(SET_GIO, EXIT_SET_TIME);
			rotary->setEncoderValue(EXIT_SET_TIME);
		}
		break;
	}
	case EXIT_SET_TIME:
	{
		STATE_MENU_SETTING = HOME_NEMU;

		rotary->setBoundaries(SETTING_LUU_LUONG, EXIT_MENU);
		rotary->setEncoderValue(SETTING_THOI_GIAN);
		break;
	}
	}
}
void MLDwinRotary::updateStateThongTinHenGio()
{
	switch (STATE_TT_HEN_GIO)
	{
	case HOME_TT_HEN_GIO:
	{
		updateDisplayPageDwin();
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			STATE_TT_HEN_GIO = static_cast<MLDwinRotary::TT_HEN_GIO>(rotary->readEncoder());
		}

		break;
	}
	case EXIT_TT_HEN_GIO:
	{
		STATE_MENU_SETTING = HOME_NEMU;
		dwin->setBuzzer(0x000A);
		rotary->setBoundaries(SETTING_LUU_LUONG, EXIT_MENU);
		rotary->setEncoderValue(THONG_TIN_HEN_GIO);
		break;
	}
	}
}
void MLDwinRotary::updateValueDwin()
{
	static unsigned long preTimeUpdate = millis();
	if (millis() - preTimeUpdate > 500)
	{
		dwin->writeVP(TIME_VAN_A_ADDR, dwinSensorValue.time_van[0]);
		dwin->writeVP(TIME_VAN_B_ADDR, dwinSensorValue.time_van[1]);
		dwin->writeVP(TIME_VAN_C_ADDR, dwinSensorValue.time_van[2]);
		dwin->writeVP(TIME_VAN_D_ADDR, dwinSensorValue.time_van[3]);

		dwin->writeVP(FLOW_A_ADDR, dwinSensorValue.luu_luong[0]);
		dwin->writeVP(FLOW_B_ADDR, dwinSensorValue.luu_luong[1]);
		dwin->writeVP(FLOW_C_ADDR, dwinSensorValue.luu_luong[2]);
		dwin->writeVP(FLOW_D_ADDR, dwinSensorValue.luu_luong[3]);

		dwin->writeVP(VOLUME_FLOW_A_ADDR, dwinSensorValue.volume_flow[0]);
		dwin->writeVP(VOLUME_FLOW_B_ADDR, dwinSensorValue.volume_flow[1]);
		dwin->writeVP(VOLUME_FLOW_C_ADDR, dwinSensorValue.volume_flow[2]);
		dwin->writeVP(VOLUME_FLOW_D_ADDR, dwinSensorValue.volume_flow[3]);

		dwin->writeVP(TEMP_ADDR, dwinSensorValue.nhiet_do);
		dwin->writeVP(HUMI_ADDR, dwinSensorValue.do_am);
		dwin->writeVP(EC_ADDR, dwinSensorValue.EC);

		preTimeUpdate = millis();
	}
}
void MLDwinRotary::setSensorData(MLDwinSensorValue data)
{
	dwinSensorValue = data;
}
MLDwinRotary::MLDwinRotary(Stream *_port, uint8_t clk, uint8_t dt, uint8_t sw, uint8_t step)
{
	rotary = new AiEsp32RotaryEncoder(clk, dt, sw, -1, step);
	dwin = new MLDwin(_port);
}
void MLDwinRotary::begin(void (*callback)(void))
{
	rotary->begin();
	rotary->setup(callback);
	DW_PAGE = HOME_PAGE;
	STATE_MENU_SETTING = HOME_NEMU;
	STATE_SETTING_LL = HOME_SETTING_LL;
	delay(2000);
	dwin->dwinShowPage(0);
	delay(4000);
}
void MLDwinRotary::readEncoder_isr()
{
	rotary->readEncoder_ISR();
}
void MLDwinRotary::testLoop()
{
	static unsigned long preTime = millis();
	String data = "123456789         ";
	if (millis() - preTime > 500)
	{
		preTime = millis();
	}
	if (rotary->encoderChanged())
	{
		Serial.print("Value: ");
		Serial.println(rotary->readEncoder());
		dwin->writeVP(0x1000, (int)rotary->readEncoder());
		dwin->writeVP(0x1001, (int)rotary->readEncoder());
		dwin->writeVP(0x1002, (int)rotary->readEncoder());
		dwin->writeVP(0x1003, (int)rotary->readEncoder());
	}
	if (rotary->isEncoderButtonClicked())
	{
		static unsigned long lastTimePressed = 0;

		if (millis() - lastTimePressed < 500)
		{
			return;
		}
		dwin->setBuzzer(0x000A);
		lastTimePressed = millis();
		Serial.print("button pressed ");
		Serial.print(millis());
		Serial.println(" milliseconds after restart");
	}
}
void MLDwinRotary::run()
{
	switch (DW_PAGE)
	{
	case HOME_PAGE:
	{
		updateValueDwin();
		if (dwin->getIndexPage() != HOME_PAGE)
		{
			dwin->dwinShowPage(HOME_PAGE);
		}
		if (rotary->isEncoderButtonClicked())
		{
			dwin->setBuzzer(0x000A);
			Serial.println("HOME PAGE to SETTING");
			rotary->setEncoderValue(SETTING_LUU_LUONG);
			rotary->setBoundaries(SETTING_LUU_LUONG, EXIT_MENU);
			DW_PAGE = SETTING_PAGE;
			STATE_MENU_SETTING = HOME_NEMU;
		}
		break;
	}
	case SETTING_PAGE:
	{
		updateStateMenu();
		break;
	}
	}
}
MLDwinRotary::~MLDwinRotary()
{
}
