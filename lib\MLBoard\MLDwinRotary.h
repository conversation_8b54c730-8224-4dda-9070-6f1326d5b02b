#ifndef _ML_DWIN_ROTARY_H
#define _ML_DWIN_ROTARY_H

#include "Arduino.h"
#include "MLDwin.h"
#include "AiEsp32RotaryEncoder.h"

#define MAX_LUU_LUONG 3000
#define MAX_TIME_SET 3000
#define RTC_ADDR 0x1000
#define MODE_ADDR 0x1020
#define TIME_TUOI_ADDR 0x1040

#define TIME_VAN_A_ADDR 0x1041
#define TIME_VAN_B_ADDR 0x1042
#define TIME_VAN_C_ADDR 0x1043
#define TIME_VAN_D_ADDR 0x1044

#define FLOW_A_ADDR 0x1045
#define FLOW_B_ADDR 0x1046
#define FLOW_C_ADDR 0x1047
#define FLOW_D_ADDR 0x1048

#define VOLUME_FLOW_A_ADDR 0x1049
#define VOLUME_FLOW_B_ADDR 0x1051
#define VOLUME_FLOW_C_ADDR 0x1053
#define VOLUME_FLOW_D_ADDR 0x1055

#define TEMP_ADDR 0x1057
#define HUMI_ADDR 0x1059
#define EC_ADDR 0x1060

#define SET_FLOW_A_ADDR 0x1062
#define SET_FLOW_B_ADDR 0x1063
#define SET_FLOW_C_ADDR 0x1064
#define SET_FLOW_D_ADDR 0x1065

#define SET_TIME_FRESH_WATER_ADDR 0x1066
#define SET_TIME_CH_A_ADDR 0x1067
#define SET_TIME_CH_B_ADDR 0x1068
#define SET_TIME_CH_C_ADDR 0x1069
#define SET_TIME_CH_D_ADDR 0x1070

#define SET_HH_ADDR 0x1071
#define SET_MM_ADDR 0x1072
#define SET_DDD_ADDR 0x1073
#define SET_MMM_ADDR 0x1074
#define SET_YYY_ADDR 0x1075

#define INDEX_SCHEDLE_ADDR 0x1076
#define SCHEDULE_DD_ADR 0x1077
#define SCHEDULE_HH_ADR 0x1078
#define SCHEDULE_MM_ADR 0x1079
#define SCHEDULE_REPEAT_ADR 0x1080
struct MLDwinSensorValue
{
    uint16_t luu_luong[4] = {0, 0, 0, 0};
    float volume_flow[4] = {0.0, 0.0, 0.0, 0.0};
    uint16_t time_van[4] = {0, 0, 0, 0};
    float nhiet_do = 0;
    int do_am = 0;
    float EC = 0;
};
struct MLDwinSetData
{

    String mode = "AUTO";
    uint16_t set_luu_luong[4] = {0, 0, 0, 0};
    uint16_t set_thoi_gian_tuoi[5] = {0, 0, 0, 0};
    uint16_t set_time_rtc[5] = {0, 0, 0, 0};
};
class MLDwinRotary
{
private:
    AiEsp32RotaryEncoder *rotary;
    MLDwin *dwin;
    const uint16_t MAX_SETTING_TIME_VALUE[5] = {23, 60, 31, 12, 4000};
    enum SETING
    {
        HOME_NEMU = 2,
        SETTING_LUU_LUONG = 3,
        SETTING_THOI_GIAN_TUOI = 4,
        SETTING_THOI_GIAN = 5,
        THONG_TIN_HEN_GIO = 6,
        EXIT_MENU = 7

    } STATE_MENU_SETTING;

    enum MAIN_PAGE
    {
        HOME_PAGE = 1,
        SETTING_PAGE = 2
    } DW_PAGE;

    enum ST_LUU_LUONG
    {
        HOME_SETTING_LL = 8,
        SETTING_MODE = 9,
        SET_LL_A = 10,
        SET_LL_B = 11,
        SET_LL_C = 12,
        SET_LL_D = 13,
        EXIT_LL = 14
    } STATE_SETTING_LL;
    enum ST_THOI_GIAN_TUOI
    {
        HOME_SETTING_TGT = 15,
        SET_TGT_NUOC = 16,
        SET_TGT_A = 17,
        SET_TGT_B = 18,
        SET_TGT_C = 19,
        SET_TGT_D = 20,
        EXIT_TGT = 21
    } STATE_SETTING_TGT;

    enum ST_THOI_GIAN
    {
        HOME_SETTING_TIME = 22,
        SET_GIO = 23,
        SET_PHUT = 24,
        SET_NGAY = 25,
        SET_THANG = 26,
        SET_NAM = 27,
        EXIT_SET_TIME = 28
    } STATE_SETTING_TIME;

    enum TT_HEN_GIO
    {
        HOME_TT_HEN_GIO = 30,
        EXIT_TT_HEN_GIO = 31
    } STATE_TT_HEN_GIO;
    void updateDisplayPageDwin();
    void updateStateMenu();
    void updateStateSettingLuuLuong();
    void updateStateSettingThoiGianTuoi();
    void updateStateSettingThoiGian();
    void updateStateThongTinHenGio();
    void updateValueDwin();

public:
    MLDwinSetData dwinSetData;
    MLDwinSensorValue dwinSensorValue;
    void setSensorData(MLDwinSensorValue data);
    MLDwinSetData getDataSetting();
    void setDataSetting(MLDwinSetData data);
    MLDwinRotary(Stream *_port, uint8_t clk, uint8_t dt, uint8_t sw, uint8_t step);
    void begin(void (*callback)(void) = NULL);
    void readEncoder_isr();
    void testLoop();
    void run();
    ~MLDwinRotary();
};

#endif