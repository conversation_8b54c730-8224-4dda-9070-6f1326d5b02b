#include "MLSensorTcp.h"
#include <ModbusIP_ESP8266.h>
#include "ModbusRTU.h"

MLSensorTcp::MLSensorTcp(ModbusIP *_mbIP)
{
    mbIP = _mbIP;
}
void MLSensorTcp::updateValve()
{
    // mb->readIreg(ID_Sensor, 0, data2, 8);

    // for(int i=0; i<4; i++)
    // {
    //     Serial.println("Data2 Sensor "+(String)i + " =: " + (String)data2[i]);
    // }
    temp = (float)mbIP->Hreg(0) / 100;
    // Serial.print("temp: ");
    // Serial.println(temp);

    humi = (float)mbIP->Hreg(1) / 100;
    // Serial.print("humi: ");
    // Serial.println(humi);

    uint32_t lux_32t = floatMidLitteEndianCDAB((uint16_t)mbIP->Hreg(2), (uint16_t)mbIP->Hreg(3));
    lux = uint32ToFloat(lux_32t);
    // Serial.print("lux: ");
    // Serial.println(lux);
}

uint32_t MLSensorTcp::floatMidLitteEndianCDAB(uint16_t AB, uint16_t CD) // cái này là ghép 2 thanh ghi 16 thành 32
{
    uint32_t CDAB = AB | (CD << 16);
    return CDAB;
}

float MLSensorTcp::uint32ToFloat(uint32_t x) // hàm này là chuyển uint32 ssang float (unti32 là 8byte)
{
    float y;
    memcpy((uint8_t *)&y, (uint8_t *)&x, 4);
    return y;
}