#ifndef _MLSensorTcp_H_
#define _MLSensorTcp_H_

#include "Arduino.h"
#include <ModbusIP_ESP8266.h>
#include "ModbusRTU.h"


class MLSensorTcp
{
private:
    ModbusIP *mbIP;

    uint16_t date2[6];
    uint16_t data2[8];
    

public:
    MLSensorTcp(ModbusIP *_mbIP);
    void updateFlowSensor();
    void updateValve();
    float temp;
    float humi;
    int lux;
    float uint32ToFloat(uint32_t x);
    uint32_t floatMidLitteEndianCDAB(uint16_t AB, uint16_t CD);
};

#endif