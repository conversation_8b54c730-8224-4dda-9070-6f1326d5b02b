#include "Ml16Board.h"
#include "dosingThingsboard2CH.h"

bool endSession = true;
bool needSetControl = false;

Ml16Board::Ml16Board(ModbusRTU *_mb)
{
    mb = _mb;
    for (int i = 0; i < 20; i++)
    {
        valveStates[i] = false;
        valveSetState[i] = false;
        currentValveStates[i] = false;
    }
}
bool cbWriteControl(Modbus::ResultCode event, uint16_t transactionId, void *data)
{
    // Serial.printf("Write event: %d\r\n", event);
    ESP.getFreeHeap();
    // Serial.printf_P("Request result: 0x%02X, Mem: %d\n", event, ESP.getFreeHeap());
    //  if (event == 0)
    //  {
    //      needSetControl = false;
    //  }
    //  else
    //  {
    //      static uint8_t countFail = 0;
    //      countFail++;
    //      if (countFail >= 5)
    //      {
    //          countFail = 0;
    //          needSetControl = false;
    //      }
    //  }
    //  endSession = true;
    return true;
}

void Ml16Board::init()
{
}
bool Ml16Board::setValveState(int valveNumber, bool state)
{
    bool status;
    // Check if the valve number is valid
    if (valveNumber >= 0 && valveNumber < 20)
    {
        valveSetState[valveNumber] = state;
        // status = mb->writeCoil(ID_IO, valveNumber, state, cbWriteControl);
    }
    return true;
}

bool Ml16Board::getValveState(int valveNumber)
{
    // Check if the valve number is valid
    if (valveNumber >= 0 && valveNumber < 20)
    {
        return valveStates[valveNumber];
    }
    return false; // Invalid valve number
}

bool Ml16Board::checkAllValveOff()
{
    for (int i = 0; i < 13; i++)
    {
        if (currentValveStates[i] == true)
        {
            //Serial.println("trang thai valve = " + (String)valveStates[i]);
            return false;
        }
    }
    return true;
}

void Ml16Board::setMainPump(bool state)
{
    valveSetState[15] = state;
    // String key = "power_5" + (String)(i + 1);
}

bool Ml16Board::getMainPump()
{
    return currentValveStates[15];
}

void Ml16Board::setAlarmLight(bool state)
{
    valveSetState[14] = state;
}
bool Ml16Board::getAlarm()
{
    return currentValveStates[14];
}
void Ml16Board::setStartLight(bool state)
{
    valveSetState[13] = state;
}

void Ml16Board::resetFlow(int flow, bool state)
{
    int k = 15 + flow;
    valveSetState[k] = state;
    mb->writeCoil(ID_IO, k, valveSetState[k], cbWriteControl);
    Serial.println("gui lenh reset meter " + (String)k);
    isReseted[flow-1] = false;
}

void Ml16Board::updateValve()
{
    // mb.readCoil(2, 0, coils, 18, cbWrite);
    mb->readCoil(ID_IO, 0, currentValveStates, 20, cbWriteControl);
    //   DB_PRINTLN("=========update modbus status==========");
    for (int i = 0; i < 20; i++)
    {
        // DB_PRINTLN(" gia tri current " +(String)i + ":  "+ (String)currentValveStates[i]) ;
        //  DB_PRINT((String)i + "  "+ (String)valveStates[i]) ;
        //  DB_PRINTLN("=========end==========");
        String key = "valve" + (String)(i + 1);
        if (valveStates[i] != currentValveStates[i])
        {
            valveStates[i] = currentValveStates[i];
            Serial.println((String)key + " : " + (String)currentValveStates[i]);
            if (thingsboard.connected())
            {
                thingsboard.sendTelemertry(key, valveStates[i]); // gui len thingsboad
            }
        }
        if (valveStates[15] != currentValveStates[15])
        {
            valveStates[15] = currentValveStates[15];
            thingsboard.sendTelemertry("power_5", valveStates[15]);
        }
    }
}

void Ml16Board::updateWriteValve()
{
    for (int k = 0; k < 20; k++)
    {
        if (k < 16)
        {
            if (currentValveStates[k] != valveSetState[k])
            {
                mb->writeCoil(ID_IO, k, valveSetState[k], cbWriteControl);
                DB_PRINTLN("write coil   " + (String)k + "  " + (String)valveSetState[k]);
            }
        }
        else
        {
            if ((currentValveStates[k] != valveSetState[k]) && currentValveStates[k] == 0)
            {
                mb->writeCoil(ID_IO, k, valveSetState[k], cbWriteControl);
            }
            if (currentValveStates[k] == 1)
            {
                isReseted[k-16] = true;
                valveSetState[k] = 0;
                mb->writeCoil(ID_IO, k, valveSetState[k], cbWriteControl);
            }
        }
    }
}

void Ml16Board::updateFlowSensor()
{
    if (mb->readIreg(ID_IO, 0, currentFlow, 8, cbWriteControl))
    {
        for (int i = 0; i < 8; i++)
        {
            currentFlow[i] = currentFlow[i]; 
            if (i < 4)
            {
                flowRate[i] = currentFlow[i]*10; // ml/phut
               // Serial.println("Flow rate : "+ (String)i + " : " + (String)currentFlow[i]);
            }
            else
            {
                flowVolume[i - 4] = currentFlow[i]*10;  // tra ml
              //  Serial.println("Tổng lưu lương : " + (String)i + " : " + (String)currentFlow[i]);
            }
        }
    }
}

float Ml16Board::getFlowRate(int val)
{
    if (val > 0 && val < 5)
    {
        return (float)flowRate[val - 1];
    }
}

float Ml16Board::getFlowVolume(int val)
{
    if (val > 0 && val < 5)
    {
        return (float)flowVolume[val - 1];
    }
}
