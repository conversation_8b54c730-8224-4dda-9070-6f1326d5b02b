#ifndef _Ml16Board_H_
#define _Ml16Board_H_

#include "Arduino.h"
#include <ModbusRTU.h>

#define ID_IO 2

class Ml16Board
{
private:
    ModbusRTU *mb;

    bool valveStates[20];
    bool valveSetState[20];
    bool currentValveStates[20];
    u_int16_t currentFlow[8];
    
public:
    Ml16Board(ModbusRTU *_mb);

    void init();

    bool setValveState(int valveNumber, bool state);

    bool getValveState(int valveNumber);
    bool checkAllValveOff();

    void updateFlowSensor();

    void setMainPump(bool state);
    bool getMainPump();
    bool getAlarm();

    void setAlarmLight(bool state);
    void setStartLight(bool state);
    void resetFlow(int flow, bool state);


    void updateValve();
    void updateWriteValve();

    float getFlowRate(int val);
    float getFlowVolume(int val);

    u_int16_t flowRate[4];
    float flowVolume[4];
    bool isReseted[4];
};

#endif