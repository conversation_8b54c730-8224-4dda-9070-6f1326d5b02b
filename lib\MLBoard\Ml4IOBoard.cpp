#include "Ml4IOBoard.h"
#include "dosingThingsboard2CH.h"


Ml4IOBoard::Ml4IOBoard(ModbusRTU *_mb)
{
    mb = _mb;
    // Initialize all valve states to false (closed)
    for (int i = 0; i < 4; i++)
    {
        relayStates[i] = false;
        currentRelayStates[i] = false;
    }
}

void Ml4IOBoard::setRelayState(int valveNumber, bool state)
{
    // Check if the valve number is valid
    if (valveNumber >= 0 && valveNumber < 4)
    {
        mb->writeCoil(ID_4IO, valveNumber, state);
        Serial.println("WriteCoil relay");
       // valveStates[valveNumber] = state;
    }
}

bool Ml4IOBoard::getRelayState(int valveNumber)
{
    // Check if the valve number is valid
    if (valveNumber >= 0 && valveNumber < 20)
    {
        return relayStates[valveNumber];
    }

    return false; // Invalid valve number
}

void Ml4IOBoard::updateValve()
{
    
    for (int i = 0; i < 4; i++)
    {
        mb->readCoil(ID_4IO, i, currentRelayStates,1);
        String key = "power" + (String)(i + 1);
        if(relayStates[i] != currentRelayStates[i])
        {
            relayStates[i] = currentRelayStates[i];
            Serial.println((String)key + " : " + (String)currentRelayStates[i]);
            if(thingsboard.connected())
            {
               // thingsboard.sendTelemertry(key, relayStates[i]);  // gui len thingsboad  
            }
              
        } 
    }
}

