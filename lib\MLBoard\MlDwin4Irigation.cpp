#include "MlDwin4Irigation.h"
#include <ModbusRTU.h>
#include <ModbusIP_ESP8266.h>

unsigned long MlDwin4Irigation::float2hex(float value)
{
   unsigned long *floatAsHex = reinterpret_cast<unsigned long *>(&value);
   unsigned long hexValue = *floatAsHex;
   // Serial.println(hexValue,HEX);

   return hexValue;
}

MlDwin4Irigation::MlDwin4Irigation(Ml16Board *_myMl16Board)
{
   myMl16Board = _myMl16Board;
   // mySensorTcp = _mySensorTcp;
}

void MlDwin4Irigation::updateScreen()
{
   MLDwin::writeVP(0x2100, time.c_str(), time.length());
   MLDwin::writeVP(0x2100, mode.c_str(), mode.length());
   MLDwin::writeVP(0x1000,timeVanA);
   MLDwin::writeVP(0x1001,timeVanB);
   MLDwin::writeVP(0x1002,timeVanC);
   MLDwin::writeVP(0x1003,timeVanD);
   MLDwin::writeVP(0x1008,timeTuoi);
   MLDwin::writeVP(0x1004, myMl16Board->flowRate[0]);
   MLDwin::writeVP(0x1005, myMl16Board->flowRate[1]);
   MLDwin::writeVP(0x1006, myMl16Board->flowRate[2]);
   MLDwin::writeVP(0x1007, myMl16Board->flowRate[3]);
   float flowVolumeA = myMl16Board->flowVolume[0] / 1000.0;
   float flowVolumeB = myMl16Board->flowVolume[1] / 1000.0;
   float flowVolumeC = myMl16Board->flowVolume[2] / 1000.0;
   float flowVolumeD = myMl16Board->flowVolume[3] / 1000.0;
   MLDwin::writeVP(0x1020, flowVolumeA);
   MLDwin::writeVP(0x1022, flowVolumeB);
   MLDwin::writeVP(0x1024, flowVolumeC);
   MLDwin::writeVP(0x1026, flowVolumeD);
   MLDwin::writeVP(0x1028, temp);
   MLDwin::writeVP(0x1030, humi);
   MLDwin::writeVP(0x1032, EC);

}
