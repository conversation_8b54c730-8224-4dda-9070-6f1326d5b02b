#ifndef _MlDwin4Irigation_H_
#define _MlDwin4Irigation_H_

// #define ID_Dwin 7

#include "Arduino.h"
#include "MlECSensor.h"
#include "MlSensorTcp.h"
#include "Ml16Board.h"
#include <ModbusRTU.h>
#include <ModbusIP_ESP8266.h>
#include <MLDwin.h>

class MlDwin4Irigation : public MLDwin
{
private:
    uint16_t data1[20];
    Ml16Board *myMl16Board;



    unsigned long float2hex(float value);
    // IPAddress *remote1= new IPAddress (192, 168, 1, 20);

public:
    MlDwin4Irigation(Ml16Board *_myMl16Board);
        String time = "";
    String mode = "";
    float temp = 0;
    float humi = 0;
    float EC = 0;

    int timeVanA=0;
    int timeVanB=0;
    int timeVanC=0;
    int timeVanD=0;

    int timeTuoi=0;
    void updateScreen();
};

#endif