#include "MlECSensor.h"


MlECSensor:: MlECSensor(ModbusRTU *_mb)
{
    mb = _mb;
}

void MlECSensor::updateValve()
{
    mb->readIreg(ID_SENSOR, 0, data, 8);

    // for (int i = 0; i < 8; i++)
    // {
    //     Serial.println("Data Sensor = " + (String)i + " : " + (String)data[i]);
    // }
    EC = (float)data[0] / 100;
    // Serial.print("EC: ");
    // Serial.println(EC);

    hh = (float)data[5];
    // Serial.print("time: ");
    // Serial.print(hh);

    mm = (float)data[6];
    // Serial.print("/");
    // Serial.print(mm);

    ss = (float)data[7];
    // Serial.print("/");
    // Serial.print(ss);

    rank = (float)data[4];
    // Serial.print("-");
    // Serial.print(rank);

    dd = (float)data[3];
    // Serial.print("-");
    // Serial.print(dd);

    MM = (float)data[2];
    // Serial.print("/");
    // Serial.print(MM);

    yy = (float)data[1];
    // Serial.print("/");
    // Serial.println(yy);
}

float MlECSensor::getEcValue()
{
    return EC;
}
int MlECSensor::dayOfWeek()
{
}
int MlECSensor::getDay()
{
}
int MlECSensor::getMonth()
{

}