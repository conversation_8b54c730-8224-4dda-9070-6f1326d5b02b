#ifndef _MlSensor_H_
#define _MlSensor_H_

#define ID_SENSOR 3
#include "Arduino.h" 
#include <ModbusRTU.h>

class MlECSensor
{
private:
    ModbusRTU *mb;
    uint16_t date[6];
    uint16_t data[8];

public:
    MlECSensor(ModbusRTU *_mb);
    void updateFlowSensor();
    void updateValve();
    float getEcValue();
    int dayOfWeek();
    int getDay();
    int getMonth();

    float EC;
    int yy, MM, dd, hh, mm, ss, rank;
};

#endif