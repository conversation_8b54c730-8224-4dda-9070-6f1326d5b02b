#include "MLDwin.h"
MLDwin::MLDwin(/* args */)
{
}
MLDwin::MLDwin(Stream *_port)
{
    myPort = _port;
}
MLDwin::~MLDwin()
{
}
void MLDwin::dwinShowPage(int page)
{
   
    if (indexPage != page)
    {
        indexPage = page;
        Serial.println("page: " + (String)indexPage);
        byte bValL, bValH;
        bValL = page & 0xFF;
        bValH = (page >> 8) & 0xFF;
        myPort->write(0x5A);
        myPort->write(0xA5);
        myPort->write(0x07);
        myPort->write(0x82);
        myPort->write((byte)0x00);
        myPort->write(0x84);
        myPort->write(0x5A);
        myPort->write(0x01);
        myPort->write(bValH);
        myPort->write(bValL);
    }
}
void MLDwin::begin(Stream *_port)
{
    myPort = _port;
}
void MLDwin::writeVP(int iAdr, int iVal)
{
    byte bAdrL, bAdrH, bValL, bValH;
    bAdrL = iAdr & 0xFF;
    bAdrH = (iAdr >> 8) & 0xFF;
    bValL = iVal & 0xFF;
    bValH = (iVal >> 8) & 0xFF;
    myPort->write(0x5A);
    myPort->write(0xA5);
    myPort->write(0x05);
    myPort->write(0x82);
    myPort->write(bAdrH);
    myPort->write(bAdrL);
    myPort->write(bValH);
    myPort->write(bValL);
}

void MLDwin::writeVP(int iAddr, float Value)
{
    IntToHex(iAddr, Add_Value);
    FloatToHex(Value, Hex_Value);
    myPort->write(0x5A);
    myPort->write(0xA5);
    myPort->write(0x05);
    myPort->write(0x82);
    myPort->write(Add_Value[1]);
    myPort->write(Add_Value[0]);
    myPort->write(Hex_Value[3]);
    myPort->write(Hex_Value[2]);
    myPort->write(Hex_Value[1]);
    myPort->write(Hex_Value[0]);
}
void MLDwin::writeVP(uint16_t VPAddr, const char *value, int length)
{
    IntToHex(VPAddr, Add_Value);
    myPort->write(0x5A);
    myPort->write(0xA5);
    myPort->write(0x0F);
    myPort->write(0x82);
    myPort->write(Add_Value[1]);
    myPort->write(Add_Value[0]);
    myPort->write(value, length);
    // myPort->write("123444",6);
}
void MLDwin::writeVP(uint16_t VPAddr, String value)
{
    IntToHex(VPAddr, Add_Value);
    myPort->write(0x5A);
    myPort->write(0xA5);
    myPort->write(0x0F);
    myPort->write(0x82);
    myPort->write(Add_Value[1]);
    myPort->write(Add_Value[0]);
    myPort->write(value.c_str(), value.length() + 1);
}
void MLDwin::FloatToHex(float f, byte *hex)
{

    byte *f_byte = reinterpret_cast<byte *>(&f);
    memcpy(hex, f_byte, 4);
}
void MLDwin::IntToHex(int f, byte *hex)
{

    byte *f_byte = reinterpret_cast<byte *>(&f);
    memcpy(hex, f_byte, 2);
}

void MLDwin::setBuzzer(int time)
{
    myPort->write(0x5A);
    myPort->write(0xA5);
    myPort->write(0x05);
    myPort->write(0x82);
    myPort->write((byte)0x00);
    myPort->write(0xA0);
    myPort->write(highByte(time));
    myPort->write(lowByte(time));
}

int MLDwin::getIndexPage()
{
    return indexPage;
}
