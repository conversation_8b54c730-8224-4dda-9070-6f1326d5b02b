#ifndef _ML_DWIN_H_
#define _ML_DWIN_H_
#include "stdint.h"
#include "HardwareSerial.h"
#include "Arduino.h"
class MLDwin
{
private:
    Stream *myPort;
    byte Hex_Value[4] = {0}, Add_Value[2] = {0};
    int indexPage=0;
public:
    MLDwin(/* args */);
    MLDwin(Stream *_port);
    ~MLDwin();
    void dwinShowPage(int page);
    void begin(Stream *_port);
    void writeVP(int iAdr, int iVal);
    void writeVP(int iAddr, float Value);
    void writeVP(uint16_t VPAddr, const char *value, int length);
      void writeVP(uint16_t VPAddr, String value);
    void FloatToHex(float f, byte *hex);
    void IntToHex(int f, byte *hex);
    void setBuzzer(int time);
    int getIndexPage();
};
#endif