#include "ML_Ethernet.h"
#include "ML_Debug.h"



static bool eth_connected = false;
void WiFiEvent(WiFiEvent_t event)
{
  switch (event) {
    case SYSTEM_EVENT_ETH_START:
      Serial.println("ETH Started");
      //set eth hostname here
      ETH.setHostname("esp32-ethernet");
      break;
    case SYSTEM_EVENT_ETH_CONNECTED:
      Serial.println("ETH Connected");
      break;
    case SYSTEM_EVENT_ETH_GOT_IP:
      Serial.print("ETH MAC: ");
      Serial.print(ETH.macAddress());
      Serial.print(", IPv4: ");
      Serial.print(ETH.localIP());
      if (ETH.fullDuplex()) {
        Serial.print(", FULL_DUPLEX");
      }
      Serial.print(", ");
      Serial.print(ETH.linkSpeed());
      Serial.println("Mbps");
      eth_connected = true;
      break;
    case SYSTEM_EVENT_ETH_DISCONNECTED:
      Serial.println("ETH Disconnected");
      eth_connected = false;
      break;
    case SYSTEM_EVENT_ETH_STOP:
      Serial.println("ETH Stopped");
      eth_connected = false;
      break;
    default:
      break;
  }
}
void ML_Ethernet::init()
{
// #if defined EXTERNAL_CLOCK

//     // pinMode(ETH_POWER_PIN_ALTERNATIVE, OUTPUT);

//     // digitalWrite(ETH_POWER_PIN_ALTERNATIVE, HIGH);
// #endif

    WiFi.onEvent(WiFiEvent);
    ETH.begin(ETH_ADDR, ETH_POWER_PIN, ETH_MDC_PIN, ETH_MDIO_PIN, ETH_TYPE, ETH_CLK_MODE);
}
void ML_Ethernet::init(IPAddress local_ip, IPAddress gateway, IPAddress subnet, IPAddress primaryDNS, IPAddress secondaryDNS)
{
    _local_IP = local_ip;
    _gateway = gateway;
    _subnet = subnet;
    _dns1 = primaryDNS;
    _dns2 = secondaryDNS;
// #if defined EXTERNAL_CLOCK

//     // pinMode(ETH_POWER_PIN_ALTERNATIVE, OUTPUT);
//     // digitalWrite(ETH_POWER_PIN_ALTERNATIVE, HIGH);
// #endif
    WiFi.onEvent(WiFiEvent);
    ETH.begin(ETH_ADDR, ETH_POWER_PIN, ETH_MDC_PIN, ETH_MDIO_PIN, ETH_TYPE, ETH_CLK_MODE);

    ETH.config(_local_IP, _gateway, _subnet, _dns1, _dns2);
}
bool ML_Ethernet::ethernetIsConnected()
{
    return eth_connected;
}

void ML_Ethernet::setDHCP(bool state)
{
    _DHCP = state;
}
IPAddress ML_Ethernet::getLocalIP(){
    return ETH.localIP();
}
