#include "ML_EthernetWiFi.h"
#include "ML_Debug.h"
#include "NetworkService.h"
#include "MLFs.h"
WifiInforTypeDefStruct wifi_infor;

static bool eth_connected = false;
static bool eth_plug = false;
// const char *ssid = "Hoa Hien 1";
// const char *password = "";

bool set_only_connection = true;

void WiFiStationConnected(WiFiEvent_t event, WiFiEventInfo_t info)
{
    DB_PRINTLN("Connected to AP successfully!");
}

void WiFiGotIP(WiFiEvent_t event, WiFiEventInfo_t info)
{
    DB_PRINTLN("WiFi connected");
    DB_PRINTLN("IP address: ");
    DB_PRINTLN(WiFi.localIP());
}

void WiFiStationDisconnected(WiFiEvent_t event, WiFiEventInfo_t info)
{
    DB_PRINTLN("Disconnected from WiFi access point");
    DB_PRINT("WiFi lost connection. Reason: ");
    DB_PRINTLN(info.disconnected.reason);
    DB_PRINTLN("Trying to Reconnect");
    if (!eth_plug)
    {
        WiFi.begin(wifi_infor.ssid.c_str(), wifi_infor.password.c_str());
    }
}

void WiFiEvent(WiFiEvent_t event)
{
    switch (event)
    {
    case SYSTEM_EVENT_ETH_START:
        DB_PRINTLN("ETH Started");
        // set eth hostname here
        ETH.setHostname("esp32-ethernet");
        break;
    case SYSTEM_EVENT_ETH_CONNECTED:
        DB_PRINTLN("ETH Connected");
        if (set_only_connection)
        {
            eth_plug = true;
            WiFi.disconnect(true);
        }
        break;
    case SYSTEM_EVENT_ETH_GOT_IP:
        DB_PRINT("ETH MAC: ");
        DB_PRINT(ETH.macAddress());
        DB_PRINT(", IPv4: ");
        DB_PRINT(ETH.localIP());
        if (ETH.fullDuplex())
        {
            DB_PRINT(", FULL_DUPLEX");
        }
        DB_PRINT(", ");
        DB_PRINT(ETH.linkSpeed());
        DB_PRINTLN("Mbps");
        eth_connected = true;
        break;
    case SYSTEM_EVENT_ETH_DISCONNECTED:
        DB_PRINTLN("ETH Disconnected");
        eth_plug = false;
        eth_connected = false;
        if (set_only_connection)
        {
            WiFi.begin(wifi_infor.ssid.c_str(), wifi_infor.password.c_str());
        }

        break;
    case SYSTEM_EVENT_ETH_STOP:
        DB_PRINTLN("ETH Stopped");
        eth_plug = false;
        eth_connected = false;
        if (set_only_connection)
        {
            WiFi.begin(wifi_infor.ssid.c_str(), wifi_infor.password.c_str());
        }
        break;
    default:
        break;
    }
}
ML_EthernetWiFi::ML_EthernetWiFi(/* args */)
{
}

ML_EthernetWiFi::~ML_EthernetWiFi()
{
}

void ML_EthernetWiFi::init()
{
    MLFs::init();
    wifi_infor = NetworkService.getConnectedWifiInfor();
    WiFi.disconnect(true);
    WiFi.onEvent(WiFiStationConnected, WiFiEvent_t::SYSTEM_EVENT_STA_CONNECTED);
    WiFi.onEvent(WiFiGotIP, WiFiEvent_t::SYSTEM_EVENT_STA_GOT_IP);
    WiFi.onEvent(WiFiStationDisconnected, WiFiEvent_t::SYSTEM_EVENT_STA_DISCONNECTED);
    // WiFi.begin(wifi_infor.ssid, wifi_infor.password);
    WiFi.onEvent(WiFiEvent);
    ETH.begin(ETH_ADDR, ETH_POWER_PIN, ETH_MDC_PIN, ETH_MDIO_PIN, ETH_TYPE, ETH_CLK_MODE);
}

IPAddress ML_EthernetWiFi::getLocalIP()
{
    return ETH.localIP();
}
