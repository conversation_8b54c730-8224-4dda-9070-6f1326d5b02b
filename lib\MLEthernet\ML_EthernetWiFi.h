#ifndef ML_ETH_WIFI_H_
#define ML_ETH_WIFI_H_
#include "Arduino.h"
#include "ETH.h"
#include "WiFi.h"
#include "MLFs.h"
#define EXTERNAL_CLOCK

#if defined INTERNAL_CLOCK

#define ETH_ADDR 1
#define ETH_POWER_PIN -1
#define ETH_MDC_PIN 23
#define ETH_MDIO_PIN 18
#define ETH_TYPE ETH_PHY_LAN8720
#define ETH_CLK_MODE ETH_CLOCK_GPIO17_OUT
#endif

#if defined EXTERNAL_CLOCK
#define ETH_ADDR 1
#define ETH_POWER_PIN 14
// #define ETH_POWER_PIN_ALTERNATIVE 14
#define ETH_MDC_PIN 23
#define ETH_MDIO_PIN 18
#define ETH_TYPE ETH_PHY_LAN8720
#define ETH_CLK_MODE ETH_CLOCK_GPIO0_IN
#endif

class ML_EthernetWiFi: public MLFs, public WiFiGenericClass
{
private:

public:
    ML_EthernetWiFi(/* args */);
    ~ML_EthernetWiFi();
    void init();
    IPAddress getLocalIP();
    
    
};

#endif