#include "ML_NTP.h"
ML_NTP::ML_NTP(/* args */)
{
    ntpUDP = new WiFiUDP();
    timeClient = new NTPClient(*ntpUDP, "vn.pool.ntp.org");
}
ML_NTP::~ML_NTP()
{
}

void ML_NTP::begin()
{
    timeClient->begin();
    timeClient->setTimeOffset(7 * 3600);
}

void ML_NTP::printLocalTime()
{
 
    Serial.println(timeClient->getFormattedTime());
}
bool ML_NTP::syncTime()
{
    return timeClient->update();
}
unsigned long ML_NTP::getEpochTime()
{
    return timeClient->getEpochTime();
}
