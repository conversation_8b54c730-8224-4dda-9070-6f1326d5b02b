#include "MLtime.h"
MLtime ML_time;
MLtime::MLtime(/* args */)
{
}

MLtime::~MLtime()
{
}

void MLtime::begin()
{
    configTime(7 * 3600, 0, "vn.pool.ntp.org");
    time_t now = time(nullptr);
    unsigned long timeOut = millis();
    while (now < 1609459200)
    {
        delay(500);
        Serial.print(".");
        now = time(nullptr);
        if (millis() - timeOut > 30000)
        {
            break;
        }
    }
    Serial.println("Time is synchronized");
}

bool MLtime::updateTime()
{
    if (!getLocalTime(&timeinfo))
    {
        Serial.println("Failed to obtain time");
        return false;
    }
    return true;
}

String MLtime::getStringTime()
{
    String hour;
    timeinfo.tm_hour > 9 ? hour = (String)timeinfo.tm_hour : hour = "0" + (String)timeinfo.tm_hour;
    String _time;
    timeinfo.tm_min > 9 ? _time = hour + ":" + (String)timeinfo.tm_min : _time = hour + ":0" + (String)timeinfo.tm_min;
    return _time;
}
String MLtime::getDayOfWeek()
{
    return (String)timeinfo.tm_wday;
}

int MLtime::getMM()
{
    return timeinfo.tm_min;
}

tm MLtime::getStructTime()
{
    return timeinfo;
}
void MLtime::printLocalTime()
{
    struct tm timeinfo;
    if (!getLocalTime(&timeinfo))
    {
        
        Serial.println("Failed to obtain time");
        return;
    }
    Serial.println(&timeinfo, "%A, %B %d %Y %H:%M:%S");
    Serial.print("Day of week: ");
    Serial.println(&timeinfo, "%A");
    Serial.print("Month: ");
    Serial.println(&timeinfo, "%B");
    Serial.print("Day of Month: ");
    Serial.println(&timeinfo, "%d");
    Serial.print("Year: ");
    Serial.println(&timeinfo, "%Y");
    Serial.print("Hour: ");
    Serial.println(&timeinfo, "%H");
    Serial.print("Hour (12 hour format): ");
    Serial.println(&timeinfo, "%I");
    Serial.print("Minute: ");
    Serial.println(&timeinfo, "%M");
    Serial.print("Second: ");
    Serial.println(&timeinfo, "%S");

    Serial.println("Time variables");
    char timeHour[3];
    strftime(timeHour, 3, "%H", &timeinfo);
    Serial.println(timeHour);
    char timeWeekDay[10];
    strftime(timeWeekDay, 10, "%A", &timeinfo);
    Serial.println(timeWeekDay);
    Serial.println();
    
}

