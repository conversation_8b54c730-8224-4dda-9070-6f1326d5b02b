#include "MLTBoard8CH__ESPS3.h"

void MLTBoard8CH__ESPS3::begin()
{
    pinMode(I1, INPUT);
    pinMode(I2, INPUT);
    pinMode(I3, INPUT);
    pinMode(I4, INPUT);
    pinMode(I5, INPUT);
    pinMode(I6, INPUT);
    pinMode(I7, INPUT);
    pinMode(I8, INPUT);

    pinMode(Q1, OUTPUT);
    pinMode(Q2, OUTPUT);
    pinMode(Q3, OUTPUT);
    pinMode(Q4, OUTPUT);
    pinMode(Q5, OUTPUT);
    pinMode(Q6, OUTPUT);
    pinMode(Q7, OUTPUT);
    pinMode(Q8, OUTPUT);

    if (!rtc->begin())
    {
        Serial.println("Could not find RTC");
        while (1)
            ;
    }
    // if (rtc->lostPower())
    // {
    //     // this will adjust to the date and time at compilation
    //     rtc->adjust(DateTime(F(__DATE__), F(__TIME__)));
    // }

    // rtc->disable32K();
}
void MLBoard8CH::testInput()
{
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], digitalRead(input[i]));
    }
}
void MLBoard8CH::testOutput()
{
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], !digitalRead(output[i]));
    }
}
void MLBoard8CH::testRelay()
{
    for (int i = 0; i < 8; i++)
    {
        digitalWrite(output[i], !digitalRead(output[i]));
    }
}
void MLBoard8CH::testRTCPrint()
{
    DateTime now = rtc->now();
    Serial.print(now.year(), DEC);
    Serial.print('/');
    Serial.print(now.month(), DEC);
    Serial.print('/');
    Serial.print(now.day(), DEC);
    Serial.print(" ");
    Serial.print(now.hour(), DEC);
    Serial.print(':');
    Serial.print(now.minute(), DEC);
    Serial.print(':');
    Serial.print(now.second(), DEC);
    Serial.println();
}
// đồng bộ thời gian từ internet
void MLBoard8CH::adjustTime(unsigned long time)
{

    DateTime now = time;
    rtc->adjust(now);
}
int MLBoard8CH::getDayOfWeek()
{
    DateTime now = rtc->now();
    return now.dayOfTheWeek();
}
String MLBoard8CH::getStringDayOfWeek()
{
    DateTime now = rtc->now();
    String dayOfWeek = (String)now.dayOfTheWeek();
    return dayOfWeek;
}
String MLBoard8CH::getTimeString()
{
    DateTime now = rtc->now();
    String hour;
    now.hour() > 9 ? hour = (String)now.hour() : hour = "0" + (String)now.hour();
    String _time;
    now.minute() > 9 ? _time = hour + ":" + (String)now.minute() : _time = hour + ":0" + (String)now.minute();
    return _time;
}
MLBoard8CH::MLBoard8CH(/* args */)
{
    rtc = new RTC_DS1307();
}

MLBoard8CH::~MLBoard8CH()
{
}