#ifndef _ML_BOARD_8CH_H_
#define _ML_BOARD_8CH_H_
#include "Arduino.h"
#include "RTClib.h"

#define I1 20
#define I2 19
#define I3 21
#define I4 42
#define I5 41
#define I6 40
#define I7 39
#define I8 38

#define Q1 1
#define Q2 2
#define Q3 3
#define Q4 4
#define Q5 5
#define Q6 6
#define Q7 15
#define Q8 26

// #define I2C_SCL 22
// #define I2C_SA 21

#define UART_TI 17
#define UART_RI 18
const uint8_t input[8] = {1, 2, 3, 4, 5, 6, 15, 16};
const uint8_t output[8] = {20, 19, 21, 42, 41, 40, 39, 38};

class MLTBoard8CH__ESPS3
{
private:
    RTC_DS3231 *rtc; 

public:
    void begin();
    void testInput();
    void testOutput();
    void testRelay();
    void testRTCPrint();
    void adjustTime(unsigned long time);
    int getDayOfWeek();
    String getStringDayOfWeek();
    String getTimeString();
    MLBoard8CH(/* args */);
    ~MLBoard8CH();
};

#endif