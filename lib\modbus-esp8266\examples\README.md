# Examples and API explanation

## [RTU](RTU)

ModbusRTU master and slave examples

## [TCP ESP8266/ESP32](TCP-ESP)

ModbusTCP for ESP8266/ESP32 client and server examples

## [TCP Ethernet W5x00](TCP-Ethernet)

ModbusTCP for W5x00 Ethernet library client and server examples (for all Arduino).

## [TLS ESP8266/ESP32](TLS)

ModbusTCP Security for ESP8266 and ESP32 (client only) examples.

## [Callbacks usage](Callback)

Examples of using callback functions.

## [Files operations](Files)

Modbus file operations examples.

## [Basic Modbus RTU to Modbus TCP bridge](bridge)

Very basic example of accessing ModbusRTU slave device connected to ESP8266/ESP32 via ModbusTCP server.

# Modbus Library for Arduino
### ModbusRTU, ModbusTCP and ModbusTCP Security

(c)2020 [<PERSON>](mailto:<EMAIL>)

The code in this repo is licensed under the BSD New License. See LICENSE.txt for more info.
