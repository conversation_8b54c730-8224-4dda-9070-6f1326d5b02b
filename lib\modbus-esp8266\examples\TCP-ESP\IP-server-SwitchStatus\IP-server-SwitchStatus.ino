/*
  Modbus-Arduino Example - Test Holding Register (Modbus IP ESP8266)
  Read Switch Status on pin GPIO0 
  Original library
  Copyright by <PERSON>
  http://github.com/andresarmento/modbus-arduino

  Current version
  (c)2017 <PERSON> (<EMAIL>)
  https://github.com/emelianov/modbus-esp8266
*/

#ifdef ESP8266
 #include <ESP8266WiFi.h>
#else //ESP32
 #include <WiFi.h>
#endif
#include <ModbusIP_ESP8266.h>

//Modbus Registers Offsets
const int SWITCH_ISTS = 100;
//Used Pins
const int switchPin = 0; //GPIO0

//ModbusIP object
ModbusIP mb;

void setup() {
    Serial.begin(115200);

    WiFi.begin("your_ssid", "your_password");
    while (WiFi.status() != WL_CONNECTED) {
      delay(500);
      Serial.print(".");
    }
    //Config Modbus IP
    mb.server();
    //Set ledPin mode
    pinMode(switchPin, INPUT);
    // Add SWITCH_ISTS register - Use addIsts() for digital inputs
    mb.addIsts(SWITCH_ISTS);
}

void loop() {
   //Call once inside loop() - all magic here
   mb.task();

   //Attach switchPin to SWITCH_ISTS register
   mb.Ists(SWITCH_ISTS, digitalRead(switchPin));
   delay(10);
}
