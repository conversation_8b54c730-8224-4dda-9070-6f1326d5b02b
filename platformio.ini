; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
; default_envs = adafruit_feather_esp32s3
default_envs = ESP32
; src_dir = application\MLT_8IO_485
; src_dir = application\MLT_SS_GATEWAY
; src_dir = application\MLT_TCP_TO_RTU
; src_dir = application/MLT_8IO_FANLIGHT
; src_dir = application/MLT_8IO_FANLIGHT_S3
src_dir = application\MLT_EC
; src_dir = application\MLT_8IO_NOIHAP
; src_dir = application\MLT_8IO_TRAI_HEO
; src_dir = application\MLT_8IO_485_ESPS3
; src_dir = application\MLT_MUSHROOM
; src_dir = application\Mlt_tiemnuoc_phoinam


[env]
platform = espressif32@3.5.0
framework = arduino
board_build.partitions = min_spiffs.csv
build_flags = 
	-DCORE_DEBUG_LEVEL=1
monitor_filters = 
	esp32_exception_decoder
	time
lib_deps_builtin = 
	SPI
	Wire
	SPIFFS
	Fs
	WiFi
	HTTPClient
	WiFiClientSecure
	Update
	ESP Async WebServer
lib_deps_external = 
	knolleary/PubSubClient@^2.8
	me-no-dev/AsyncTCP@^1.1.1
	me-no-dev/ESP Async WebServer@^1.2.3
	; ayushsharma82/AsyncElegantOTA@^2.2.7
	https://github.com/adafruit/RTClib.git
	https://github.com/nhannt2528/ModbusRTUMaster.git#1.0.0
	adafruit/Adafruit BusIO@^1.15.0
	arduino-libraries/NTPClient@^3.2.1


[env:adafruit_feather_esp32s3]
platform = espressif32
board = esp32-s3-1un4
framework = arduino
monitor_speed=115200
lib_deps_builtin = 
	SPI
	Wire
	SPIFFS
	Fs
	WiFi
	HTTPClient
	WiFiClientSecure
	Update
	ESP Async WebServer
lib_deps_external = 	knolleary/PubSubClient@^2.8
	me-no-dev/AsyncTCP@^1.1.1
	me-no-dev/ESP Async WebServer@^1.2.3
	; ayushsharma82/AsyncElegantOTA@^2.2.7
	https://github.com/adafruit/RTClib.git
	https://github.com/nhannt2528/ModbusRTUMaster.git#1.0.0
	adafruit/Adafruit BusIO@^1.15.0
	arduino-libraries/NTPClient@^3.2.1


[env:ESP32]
board = esp32doit-devkit-v1
lib_deps = 
	${env.lib_deps_builtin}
	${env.lib_deps_external}
	xreef/PCF8575 library@^1.0.3
	denyssene/SimpleKalmanFilter@^0.1.0
	https://github.com/me-no-dev/ESPAsyncWebServer.git
	igorantolic/Ai Esp32 Rotary Encoder@^1.6
	dfrobot/DFRobot_DS1307@^1.0.0
	; ayushsharma82/AsyncElegantOTA @ ^2.2.5
	sandeepmistry/CAN@^0.3.1
	; ayushsharma82/ElegantOTA@2.2.9

[env:ESP32_GATEWAY]
board = esp32doit-devkit-v1
build_flags = 
	-D EXTERNAL_CLOCK
lib_deps = 
	${env.lib_deps_builtin}
	${env.lib_deps_external}
	sandeepmistry/CAN@^0.3.1
	; ayushsharma82/ElegantOTA@2.2.9

[env:ESP32_RGB]
board = esp32doit-devkit-v1
build_flags = 
	-D EXTERNAL_CLOCK
lib_deps = 
	${env.lib_deps_builtin}
	${env.lib_deps_external}
	sandeepmistry/CAN@^0.3.1
	https://github.com/adafruit/Adafruit_TCS34725.git
	; ayushsharma82/ElegantOTA@2.2.9
