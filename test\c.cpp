#include <HardwareSerial.h>
#include <ArduinoJson.h> // Th<PERSON> viện ArduinoJson
#include <ModbusRTU.h>

#define SLAVE_ID 1 // ID của thiết bị Modbus Slave
ModbusRTU mb;
float ec_sensor, ph_sensor, temp_sensor;

void setup()
{
    // C<PERSON>u hình UART
    Serial2.begin(9600, SERIAL_8N1, 32, 33); // RX=IO32, TX=IO33 cho cảm biến
    Serial1.begin(9600, SERIAL_8N1, 25, 26); // RX=IO26, TX=IO25 cho Modbus
    Serial.begin(9600);

    // Cấu hình Modbus RTU
    mb.begin(&Serial1);
    mb.slave(SLAVE_ID);

    // Thêm các holding register cho cảm biến
    mb.addHreg(0, 0, 10);
}

void loop()
{
    static unsigned long sensorUpdateTime = 0, modbusUpdateTime = 0;

    // Cập nhật giá trị cảm biến qua UART mỗi giây
    if (millis() - sensorUpdateTime >= 1000)
    {
        sensorUpdateTime = millis();
        Serial2.println("{\"get_sensors\":1}");

        if (Serial2.available())
        {
            String response = Serial2.readString();
            StaticJsonDocument<200> doc;
            if (deserializeJson(doc, response) == DeserializationError::Ok)
            {
                ec_sensor = doc["sensors"]["ec_sensor"];
                ph_sensor = doc["sensors"]["ph_sensor"];
                temp_sensor = doc["sensors"]["temp_sensor"];
                
            }
            else
            {
                Serial.println("Failed to parse JSON!");
            }
        }
    }

    // Cập nhật giá trị Modbus mỗi 0.9 giây
    if (millis() - modbusUpdateTime >= 900)
    {
        modbusUpdateTime = millis();
        mb.Hreg(0, ec_sensor * 100);
        mb.Hreg(1, ph_sensor * 100);
        mb.Hreg(2, temp_sensor * 100);
    }

    mb.task();
    yield(); // Đảm bảo hệ thống không bị treo
}
